'use client';

import { useState, useEffect } from 'react';
import { Button } from './button';
import { X } from 'lucide-react';

interface ColorPickerProps {
  onColorSelect: (color: string) => void;
  onClose: () => void;
}

const colors = [
  '#0074b2', '#194234', '#2a9d8f', '#81d4fa', '#f295ce',
  '#fce4ec', '#b39ddb', '#bcaaa4', '#ffccbc', '#b2dfdb', '#6c9bcf',
  '#ffd552', '#39b1df', '#7986cb', '#003554'
];

const calculateTextColor = (bgColor: string): string => {
  // Convert hex to RGB
  const hex = bgColor.replace('#', '');
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light backgrounds, white for dark backgrounds
  return luminance > 0.5 ? '#000000' : '#ffffff';
};

export function ColorPicker({ onColorSelect, onClose }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState<string | null>('#0074b2');

  useEffect(() => {
    // Set default color on component mount without triggering onColorSelect
    setSelectedColor('#0074b2');
    // Update CSS custom properties for the primary color
    document.documentElement.style.setProperty('--primary', '#0074b2');
    document.documentElement.style.setProperty('--primary-foreground', calculateTextColor('#0074b2'));
  }, []);

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    // Update CSS custom properties for the primary color
    document.documentElement.style.setProperty('--primary', color);
    document.documentElement.style.setProperty('--primary-foreground', calculateTextColor(color));
    onColorSelect(color);
  };

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-card border rounded-lg shadow-lg p-6 w-[320px] space-y-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Choose a Color</h3>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-2">
          {colors.map((color) => (
            <button
              key={color}
              className={`w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg ${selectedColor === color ? 'ring-2 ring-primary' : ''}`}
              style={{ 
                backgroundColor: color,
                color: calculateTextColor(color)
              }}
              onClick={() => handleColorSelect(color)}
            >
              {selectedColor === color && '✓'}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}