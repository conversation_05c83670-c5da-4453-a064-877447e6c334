'use client';

import Link from 'next/link';
import { ShoppingCart, Heart, Menu, X, Search, Flame } from 'lucide-react';
import { useState } from 'react';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { Button } from './button';

export function MainHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, primaryColor } = useSettings();
  const { totalItems } = useCart();
  const { totalItems: wishlistTotalItems } = useWishlist();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };


  return (
    <header className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="container mx-auto px-4 flex flex-col md:flex-row items-center justify-between gap-4 py-4">
        {/* Top Row: Logo, Desktop Search, Actions/Mobile Menu */}
        <div className="flex w-full items-center justify-between gap-4">
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2 flex-shrink-0">
            <div className="flex items-center">
              <img
                src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`.replace(/\/\//g, '/')}
                alt="Logo"
                className="h-12 w-auto bg-white p-2 rounded-md"
                onError={(e) => {
                  console.error('Failed to load logo:', e.currentTarget.src);
                  // Fallback to text if image fails to load
                  e.currentTarget.outerHTML = '<span class="text-lg font-bold">CodeMedical</span>';
                }}
              />
            </div>
          </Link>

          {/* Search Input - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-4">
            <div className="flex w-full">
              <input
                type="text"
                placeholder="Search"
                className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <button
                className="p-2 rounded-r-md text-white"
                style={{ backgroundColor: primaryColor }}
              >
                <Search className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Actions & Mobile Menu Toggle */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-4">
              <Link
                href="/wishlist"
                className="p-2 rounded-full hover:bg-accent/80 transition-colors relative"
              >
                <Heart className="h-5 w-5" />
                <span
                  className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                  style={{ backgroundColor: primaryColor, color: 'white' }}
                >
                  {wishlistTotalItems}
                </span>
              </Link>
              <Link
                href="/cart"
                className="p-2 rounded-full hover:bg-accent/80 transition-colors relative"
              >
                <ShoppingCart className="h-5 w-5" />
                <span
                  className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                  style={{ backgroundColor: primaryColor, color: 'white' }}
                >
                  {totalItems}
                </span>
              </Link>
              <Link href="/login">
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                >
                  Login
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 rounded-full hover:bg-accent/80 transition-colors"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Search Input - Mobile */}
        <div className="flex w-full md:hidden mt-4">
          <input
            type="text"
            placeholder="Search"
            className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary"
          />
          <button
            className="p-2 rounded-r-md text-white"
            style={{ backgroundColor: primaryColor }}
          >
            <Search className="h-5 w-5" />
          </button>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6 mt-4 md:mt-0">
          <Link href="/" className="text-sm font-medium hover:text-primary transition-colors">
            {t('home')}
          </Link>
          <Link href="/hot-deals" className="text-sm font-medium hover:text-primary transition-colors flex items-center">
            <Flame className="h-4 w-4 mr-1" style={{ color: primaryColor }} />
            {t('hotDeals')}
          </Link>
          <Link href="/about" className="text-sm font-medium hover:text-primary transition-colors">
            {t('about')}
          </Link>
          <Link href="/contact" className="text-sm font-medium hover:text-primary transition-colors">
            {t('contact')}
          </Link>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <>
          <div className="fixed inset-0 bg-black/20 z-40 md:hidden" onClick={() => setIsMenuOpen(false)} />
          <div className="md:hidden border-t fixed top-[4.5rem] left-0 right-0 bottom-0 z-50 overflow-y-auto">
            <nav className="flex flex-col space-y-4 bg-white w-full">
              <div className="container mx-auto px-4 py-4 flex flex-col space-y-4 w-full">
                <Link
                  href="/"
                  className="w-full px-4 py-2 hover:bg-accent/80 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('home')}
                </Link>
                <Link
                  href="/hot-deals"
                  className="w-full px-4 py-2 hover:bg-accent/80 transition-colors flex items-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Flame className="h-4 w-4 mr-1" style={{ color: primaryColor }} />
                  {t('hotDeals')}
                </Link>
                <Link
                  href="/about"
                  className="w-full px-4 py-2 hover:bg-accent/80 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('about')}
                </Link>
                <Link
                  href="/contact"
                  className="w-full px-4 py-2 hover:bg-accent/80 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {t('contact')}
                </Link>
                <div className="w-full flex items-center justify-start space-x-4 px-4 py-2">
                  <Link
                    href="/wishlist"
                    className="p-2 rounded-full hover:bg-accent/80 transition-colors relative"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Heart className="h-5 w-5" />
                    <span
                      className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                      style={{ backgroundColor: primaryColor, color: 'white' }}
                    >
                      {wishlistTotalItems}
                    </span>
                  </Link>
                  <Link
                    href="/cart"
                    className="p-2 rounded-full hover:bg-accent/80 transition-colors relative"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <ShoppingCart className="h-5 w-5" />
                    <span
                      className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                      style={{ backgroundColor: primaryColor, color: 'white' }}
                    >
                      {totalItems}
                    </span>
                  </Link>
                  <Link
                    href="/login"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex-1"
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                    >
                      Login
                    </Button>
                  </Link>
                </div>
              </div>
            </nav>
          </div>
        </>
      )}


    </header>
  );
}