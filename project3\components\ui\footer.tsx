'use client';

import { Facebook, Send, MessageCircle, MessageSquare, Mail } from 'lucide-react';
import { useState } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';

export function Footer() {
  const { primaryColor, t } = useSettings();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const { executeRecaptcha } = useGoogleReCaptcha();

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!executeRecaptcha) {
      console.error('Execute recaptcha not yet available');
      return;
    }

    try {
      setIsLoading(true);
      setMessage({ type: '', text: '' });

      const token = await executeRecaptcha('subscribe');
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/insert-subscriber`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestParameters: {
            SubscriberEmail: email
          }
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Successfully subscribed!' });
        setEmail('');
      } else {
        setMessage({ type: 'error', text: data.message || 'Failed to subscribe' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <footer className="w-full">
      <div className="text-white py-8 sm:py-12" style={{ backgroundColor: primaryColor }}>
        <div className="container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* About Section */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <img
                src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`.replace(/\//g, '/')}
                alt="Logo"
                className="h-12 sm:h-16 w-auto bg-white p-2 rounded-md"
              />
            </div>
            <p className="text-gray-300 text-sm sm:text-base">
              We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time.
            </p>
            <div className="flex gap-4">
              <Link href="https://www.facebook.com/codemedicalapps/" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300 transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="https://t.me/codemedicalapps" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300 transition-colors">
                <Send className="h-5 w-5" />
              </Link>
              <Link href="https://wa.me/*************" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300 transition-colors">
                <MessageCircle className="h-5 w-5" />
              </Link>
              <Link href="https://m.me/***************" target="_blank" rel="noopener noreferrer" className="hover:text-gray-300 transition-colors">
                <MessageSquare className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-base sm:text-lg font-semibold">{t('quickLinks')}</h3>
            <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
              <li><Link href="/about" className="hover:text-gray-300 transition-colors">{t('about')}</Link></li>
              <li><Link href="/contact" className="hover:text-gray-300 transition-colors">{t('contact')}</Link></li>
              <li><Link href="/hot-deals" className="hover:text-gray-300 transition-colors">{t('hotDeals')}</Link></li>
              <li><Link href="/login" className="hover:text-gray-300 transition-colors">{t('login')}</Link></li>
              <li><Link href="/signup" className="hover:text-gray-300 transition-colors">{t('signup')}</Link></li>
            </ul>
          </div>

          {/* Customer Area */}
          <div className="space-y-4">
            <h3 className="text-base sm:text-lg font-semibold">{t('customerArea')}</h3>
            <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
              <li><Link href="/account" className="hover:text-gray-300 transition-colors">{t('myAccount')}</Link></li>
              <li><Link href="/orders" className="hover:text-gray-300 transition-colors">{t('orders')}</Link></li>
              <li><Link href="/cart" className="hover:text-gray-300 transition-colors">{t('cart')}</Link></li>
              <li><Link href="/wishlist" className="hover:text-gray-300 transition-colors">{t('wishlist')}</Link></li>
              <li><Link href="/payment-methods" className="hover:text-gray-300 transition-colors">{t('paymentMethods')}</Link></li>
            </ul>
          </div>

          {/* Contact Us */}
          <div className="space-y-4">
            <h3 className="text-base sm:text-lg font-semibold">{t('contact')}</h3>
            <ul className="space-y-2 sm:space-y-3 text-sm sm:text-base">
              <li className="flex items-center gap-2">
                <span className="text-gray-300">{t('location')}:</span>
                <span>Iraq</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="text-gray-300">{t('callUs')}:</span>
                <a href="tel:+*************" className="hover:text-gray-300 transition-colors">+964 ************</a>
              </li>
              <li className="flex items-center gap-2">
                <span className="text-gray-300">{t('emailUs')}:</span>
                <a href="mailto:<EMAIL>" className="hover:text-gray-300 transition-colors"><EMAIL></a>
              </li>
            </ul>

            {/* Newsletter Subscription */}
            <div className="mt-6">
              <h3 className="text-base sm:text-lg font-semibold mb-4">{t('newsletter')}</h3>
              <form onSubmit={handleSubscribe} className="space-y-4">
              <div className="flex flex-col gap-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('enterEmail')}
                    required
                    className="w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50"
                    style={{ outlineColor: primaryColor }}
                  />
                  <button
                    type="submit"
                    disabled={isLoading}
                    className=" px-2 py-2 border border-white rounded-md"
                    style={{ backgroundColor: primaryColor, color: 'white', borderColor: 'white' }}
                  >
                    {isLoading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
                    ) : (
                      <>
                       
                        <span>{t('subscribe')}</span>
                      </>
                    )}
                  </button>
                </div>
                {message.text && (
                  <p className={`text-sm text-center ${message.type === 'success' ? 'text-green-400' : 'text-red-400'}`}>
                    {message.text}
                  </p>
                )}
                <p className="text-xs text-gray-300 text-center">{t('newsletterDisclaimer')}</p>
                </form>
          </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-6 pt-6 border-t border-white/20 text-center text-gray-300 text-sm sm:text-base px-4">
          <p>© 2024 Code Medical. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}