import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    console.log("Product Detail API Route - Received request body:", body)

    const response = await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail", {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    console.log("Product Detail API Route - External API response status:", response.status)

    if (!response.ok) {
      console.error("Product Detail API Route - External API error:", response.status, response.statusText)
      return NextResponse.json(
        { error: `External API error: ${response.status} ${response.statusText}` },
        { status: response.status },
      )
    }

    const data = await response.json()
    console.log("Product Detail API Route - External API response data:", data)

    return NextResponse.json(data)
  } catch (error) {
    console.error("Product Detail API Route - Error:", error)
    return NextResponse.json({ error: "Failed to fetch product details" }, { status: 500 })
  }
}
