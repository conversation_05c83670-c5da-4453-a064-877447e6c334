'use client';

import { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

type ImageSliderProps = {
  images: {
    url: string;
    alt: string;
    title?: string;
    description?: string;
  }[];
  autoPlayInterval?: number;
  className?: string;
  onSlideChange?: (index: number) => void;
  initialIndex?: number;
};

export function ImageSlider({ images, autoPlayInterval = 5000, className, onSlideChange, initialIndex = 0 }: ImageSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const nextSlide = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    const newIndex = currentIndex === images.length - 1 ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
    if (onSlideChange) {
      onSlideChange(newIndex);
    }
    setTimeout(() => setIsTransitioning(false), 500);
  }, [images.length, currentIndex, onSlideChange, isTransitioning]);

  const previousSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
    if (onSlideChange) {
      onSlideChange(newIndex);
    }
    setTimeout(() => setIsTransitioning(false), 500);
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isAutoPlaying && !isTransitioning) {
      intervalId = setInterval(nextSlide, autoPlayInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isAutoPlaying, nextSlide, autoPlayInterval, isTransitioning]);

  return (
    <div
      className={cn(
        'relative w-full overflow-hidden',
        className
      )}
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      <div
        className="flex transition-transform duration-500 ease-out"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {images.map((image, index) => (
          <div
            key={index}
            className="w-full flex-shrink-0 relative"
            style={{ aspectRatio: '16/6' }} // Default aspect ratio, can be overridden by className
          >
            <div className="w-full h-full">
              <img
                src={image.url}
                alt={image.alt}
                className="w-full h-full object-cover"
                style={{ objectFit: 'cover', width: '100%', height: '100%' }}
              />
            </div>
          </div>
        ))}
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20"
        onClick={previousSlide}
        disabled={isTransitioning}
      >
        <ChevronLeft className="h-6 w-6" />
      </Button>

      <Button
        variant="ghost"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20"
        onClick={nextSlide}
        disabled={isTransitioning}
      >
        <ChevronRight className="h-6 w-6" />
      </Button>

      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20">
        {images.map((_, index) => (
          <button
            key={index}
            className={cn(
              'w-3 h-3 rounded-full transition-all',
              currentIndex === index
                ? 'bg-white scale-125'
                : 'bg-white/50 hover:bg-white/75'
            )}
            onClick={() => {
              if (!isTransitioning) {
                setIsTransitioning(true);
                setCurrentIndex(index);
                if (onSlideChange) {
                  onSlideChange(index);
                }
                setTimeout(() => setIsTransitioning(false), 500);
              }
            }}
            disabled={isTransitioning}
          />
        ))}
      </div>
    </div>
  );
}