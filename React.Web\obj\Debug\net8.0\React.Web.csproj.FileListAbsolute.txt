C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.staticwebassets.runtime.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.staticwebassets.endpoints.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.exe
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.deps.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.runtimeconfig.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\React.Web.pdb
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\unix\lib\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.AssemblyInfoInputs.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.AssemblyInfo.cs
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.RazorAssemblyInfo.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.RazorAssemblyInfo.cs
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.sourcelink.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\scopedcss\Views\Shared\_Layout.cshtml.rz.scp.css
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\scopedcss\bundle\React.Web.styles.css
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\scopedcss\projectbundle\React.Web.bundle.scp.css
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets\msbuild.React.Web.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets\msbuild.React.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets\msbuild.build.React.Web.props
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.React.Web.props
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.React.Web.props
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.csproj.Up2Date
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\refint\React.Web.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.pdb
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\React.Web.genruntimeconfig.cache
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\ref\React.Web.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical2\public\web.config
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical\public\web.config
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical2\package-lock.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical2\package.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical2\public\manifest.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical\package-lock.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical\package.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\codemedical\public\manifest.json
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\MimeKit.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Common.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Configuration.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Frameworks.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Packaging.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Protocol.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\NuGet.Versioning.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.IO.Packaging.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\de\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\es\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\it\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Downloads\ec\.NET 8 Version - Latest\project\NoorECommerce\React.Web\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
