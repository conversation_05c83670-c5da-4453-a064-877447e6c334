'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, ChevronLeft, ChevronRight, Image as ImageIcon, Video as VideoIcon } from 'lucide-react'
import { cn } from '@/lib/utils'

interface MediaItem {
  type: 'image' | 'video'
  url: string
  thumbnail?: string
  alt?: string
}

interface ProductMediaGalleryProps {
  media: MediaItem[]
  className?: string
}

export function ProductMediaGallery({ media, className }: ProductMediaGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const [activeMediaType, setActiveMediaType] = useState<'all' | 'image' | 'video'>('all')
  
  // Filter media based on active type
  const filteredMedia = media.filter(item => 
    activeMediaType === 'all' || item.type === activeMediaType
  )
  
  const currentItem = filteredMedia[currentIndex] || media[0]
  const hasMultipleItems = filteredMedia.length > 1
  
  // Reset index when media changes
  useEffect(() => {
    setCurrentIndex(0)
    setIsPlaying(false)
  }, [activeMediaType, media])
  
  const goToPrev = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? filteredMedia.length - 1 : prev - 1
    )
    setIsPlaying(false)
  }
  
  const goToNext = () => {
    setCurrentIndex((prev) => 
      prev === filteredMedia.length - 1 ? 0 : prev + 1
    )
    setIsPlaying(false)
  }
  
  const togglePlayPause = () => {
    if (currentItem.type === 'video') {
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause()
        } else {
          videoRef.current.play()
        }
        setIsPlaying(!isPlaying)
      }
    }
  }
  
  const handleVideoEnded = () => {
    setIsPlaying(false)
    if (hasMultipleItems) {
      goToNext()
    }
  }
  
  const handleThumbnailClick = (index: number) => {
    setCurrentIndex(index)
    setIsPlaying(false)
  }
  
  // Group media by type for filter buttons
  const mediaCounts = media.reduce((acc, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* Main Media Display */}
      <div className="relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden">
        {currentItem.type === 'image' ? (
          <img
            src={currentItem.url}
            alt={currentItem.alt || 'Product image'}
            className="w-full h-full object-contain"
          />
        ) : (
          <div className="relative w-full h-full">
            <video
              ref={videoRef}
              src={currentItem.url}
              className="w-full h-full object-contain"
              controls={false}
              onEnded={handleVideoEnded}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onLoadedData={() => setIsVideoLoaded(true)}
              playsInline
            />
            {!isVideoLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
                <div className="animate-pulse">Loading video...</div>
              </div>
            )}
            <button
              onClick={togglePlayPause}
              className={cn(
                'absolute inset-0 flex items-center justify-center transition-opacity',
                isPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-80',
                !isVideoLoaded && 'hidden'
              )}
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              <div className="bg-black/50 text-white rounded-full p-3">
                {isPlaying ? <Pause size={24} /> : <Play size={24} />}
              </div>
            </button>
          </div>
        )}
        
        {/* Media Type Indicator */}
        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
          {currentItem.type === 'image' ? (
            <ImageIcon size={12} />
          ) : (
            <VideoIcon size={12} />
          )}
          <span>{currentItem.type === 'image' ? 'Image' : 'Video'}</span>
        </div>
        
        {/* Navigation Arrows */}
        {hasMultipleItems && (
          <>
            <button
              onClick={goToPrev}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all"
              aria-label="Previous media"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={goToNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all"
              aria-label="Next media"
            >
              <ChevronRight size={20} />
            </button>
          </>
        )}
      </div>
      
      {/* Media Type Filter Buttons */}
      <div className="flex gap-2">
        <button
          onClick={() => setActiveMediaType('all')}
          className={cn(
            'px-3 py-1 text-sm rounded-full border',
            activeMediaType === 'all' 
              ? 'bg-blue-600 text-white border-blue-600' 
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          )}
        >
          All ({media.length})
        </button>
        {mediaCounts.image > 0 && (
          <button
            onClick={() => setActiveMediaType('image')}
            className={cn(
              'px-3 py-1 text-sm rounded-full border flex items-center gap-1',
              activeMediaType === 'image' 
                ? 'bg-blue-600 text-white border-blue-600' 
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            )}
          >
            <ImageIcon size={14} />
            <span>{mediaCounts.image}</span>
          </button>
        )}
        {mediaCounts.video > 0 && (
          <button
            onClick={() => setActiveMediaType('video')}
            className={cn(
              'px-3 py-1 text-sm rounded-full border flex items-center gap-1',
              activeMediaType === 'video' 
                ? 'bg-blue-600 text-white border-blue-600' 
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            )}
          >
            <VideoIcon size={14} />
            <span>{mediaCounts.video}</span>
          </button>
        )}
      </div>
      
      {/* Thumbnails */}
      {hasMultipleItems && (
        <div className="flex gap-2 overflow-x-auto pb-2 -mx-2 px-2">
          {filteredMedia.map((item, index) => (
            <button
              key={index}
              onClick={() => handleThumbnailClick(index)}
              className={cn(
                'relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all',
                index === currentIndex 
                  ? 'border-blue-600 ring-2 ring-blue-400' 
                  : 'border-gray-200 hover:border-gray-400'
              )}
              aria-label={`View ${item.type} ${index + 1}`}
            >
              {item.type === 'image' ? (
                <img
                  src={item.thumbnail || item.url}
                  alt={item.alt || ''}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="relative w-full h-full bg-gray-200 flex items-center justify-center">
                  <VideoIcon size={16} className="text-gray-500" />
                </div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
