'use client';

import { useState } from 'react';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useCoupon } from '@/contexts/coupon-context';
import { ShoppingCart, Trash2, ChevronRight, Plus, Minus, Check } from 'lucide-react';
import Swal from 'sweetalert2';

export default function CartPage() {
  const { t, primaryColor } = useSettings();
  const { items, removeFromCart, updateQuantity, totalItems, subtotal, total } = useCart();
  const { validateCoupon, appliedCoupon, clearCoupon } = useCoupon();
  const [couponCode, setCouponCode] = useState('');
  
  return (
    <div className="container mx-auto py-4 sm:py-6 md:py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4 sm:mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t('home')}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t('cart')}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-2">
          <h1 className="text-2xl sm:text-3xl font-bold">{t('cart')}</h1>
          <p className="text-muted-foreground">{totalItems} {totalItems === 1 ? 'item' : 'items'}</p>
        </div>
        
        {items.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {items.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <div className="flex flex-col sm:flex-row">
                    <div className="w-full sm:w-32 h-32 bg-muted relative">
                      <img 
                        src={item.image || `/products/book${item.id}.jpg`} 
                        alt={item.name} 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className="flex-1 p-4 flex flex-col sm:flex-row justify-between">
                      <div className="flex-1">
                        {/* Product Name - First Line */}
                        <h3 className="font-medium text-lg mb-2">{item.name}</h3>
                        
                        {/* Original Price and IQD Price - Second Line */}
                        <div className="flex items-center gap-4 mb-2">
                          <div className="text-base font-medium">
                            ${(item.originalPrice || item.price).toFixed(2)}
                          </div>
                          {item.iqdPrice && (
                            <div className="text-base font-medium text-muted-foreground">
                              {item.iqdPrice.toLocaleString()} IQD
                            </div>
                          )}
                        </div>
                        
                        {/* Show discount price if applicable */}
                        {item.discountPrice && item.discountPrice < (item.originalPrice || item.price) && (
                          <div className="text-sm text-green-600 mb-2">
                            Sale: <span className="font-medium">${item.discountPrice.toFixed(2)}</span>
                          </div>
                        )}
                        
                       
                        
                        {/* Display selected attributes */}
                        {item.attributes && item.attributes.length > 0 && (
                          <div className="mt-2 space-y-1  pt-2 text-sm text-muted-foreground">
                            {item.attributes.map((attr, idx) => (
                              <div key={idx}>
                                <span className="font-medium">{attr.DisplayName || attr.AttributeName}:</span>{' '}
                                {attr.AttributeValueText}
                                {attr.PriceAdjustment && (
                                  <span className="ml-1 text-green-600">
                                    ({attr.PriceAdjustmentType === 1 ? '+' : ''}${attr.PriceAdjustment} {attr.PriceAdjustmentType === 2 ? '%' : ''})
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                         {/* Final price after adjustments */}
                         <div className="text-base font-bold mb-2" style={{ color: primaryColor }}>
                          ${item.adjustedPrice.toFixed(2)}
                          {(item.adjustedPrice !== (item.originalPrice || item.price)) && (
                            <span className="text-sm font-normal text-green-600 ml-2">
                              {item.adjustedPrice > (item.originalPrice || item.price) 
                                ? `(+${(item.adjustedPrice - (item.originalPrice || item.price)).toFixed(2)})`
                                : `(-${((item.originalPrice || item.price) - item.adjustedPrice).toFixed(2)})`
                              }
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="flex items-center border rounded-md overflow-hidden">
                          <button 
                            className="p-2 hover:bg-accent/80 transition-colors"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-4 w-4" />
                          </button>
                          <span className="px-4 py-2">{item.quantity}</span>
                          <button 
                            className="p-2 hover:bg-accent/80 transition-colors"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-4 w-4" />
                          </button>
                        </div>
                        
                        <button 
                          className="p-2 text-red-500 hover:bg-accent/80 rounded-full transition-colors"
                          onClick={() => removeFromCart(item.id)}
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card>
                <div className="p-6">
                  <h2 className="text-xl font-bold mb-4">Order Summary</h2>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Subtotal</span>
                      <span>${subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Shipping</span>
                      <span>Free</span>
                    </div>
                    {appliedCoupon && (
                      <div className="flex justify-between text-green-600">
                        <span>Discount ({appliedCoupon.type === 'percentage' ? `${appliedCoupon.discount}%` : `$${appliedCoupon.discount}`})</span>
                        <span>-${(appliedCoupon.type === 'percentage' ? (subtotal * appliedCoupon.discount / 100) : appliedCoupon.discount).toFixed(2)}</span>
                      </div>
                    )}
                    <div className="border-t pt-3 mt-3 flex justify-between font-bold">
                      <span>Total</span>
                      <span style={{ color: primaryColor }}>${(total - (appliedCoupon ? (appliedCoupon.type === 'percentage' ? (subtotal * appliedCoupon.discount / 100) : appliedCoupon.discount) : 0)).toFixed(2)}</span>
                    </div>
                    <div className="mt-4">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="Enter coupon code"
                          className="flex-1 p-2 border rounded-md"
                          value={couponCode}
                          onChange={(e) => setCouponCode(e.target.value)}
                        />
                        <Button
                          onClick={() => {
                            if (!couponCode) return;
                            const result = validateCoupon(couponCode, subtotal);
                            if (result.valid) {
                              Swal.fire({
                                title: 'Success!',
                                text: result.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                              });
                              setCouponCode('');
                            } else {
                              Swal.fire({
                                title: 'Error',
                                text: result.message,
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false
                              });
                            }
                          }}
                          variant="outline"
                        >
                          Apply
                        </Button>
                      </div>
                      {appliedCoupon && (
                        <div className="flex items-center justify-between mt-2 text-sm">
                          <span className="text-green-600">Coupon {appliedCoupon.code} applied</span>
                          <button
                            onClick={() => {
                              clearCoupon();
                              Swal.fire({
                                title: 'Coupon Removed',
                                text: 'Coupon has been removed successfully',
                                icon: 'info',
                                timer: 2000,
                                showConfirmButton: false
                              });
                            }}
                            className="text-red-500 hover:underline"
                          >
                            Remove
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button 
                    className="w-full py-6"
                    style={{ backgroundColor: primaryColor }}
                    asChild
                  >
                    <Link href="/checkout">
                      Proceed to Checkout
                    </Link>
                  </Button>
                  
                  <div className="mt-4">
                    <Link 
                      href="/" 
                      className="text-sm text-center block hover:underline"
                    >
                      Continue Shopping
                    </Link>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        ) : (
          <Card>
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <ShoppingCart className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
              <p className="text-muted-foreground mb-4">Add items to your cart to proceed to checkout</p>
              <Button asChild>
                <Link href="/">
                  Continue Shopping
                </Link>
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}