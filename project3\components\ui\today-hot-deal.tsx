'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useSettings } from '@/contexts/settings-context';
import { translations } from '@/lib/translations';
import { ProductRatingStars } from '@/components/ui/product-rating-stars';
import { Flame, ShoppingBag, RefreshCw } from 'lucide-react';
import { useCart } from '@/contexts/cart-context';
import { toast } from 'sonner';
import { Config } from '@/lib/config';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  discountedPrice: number;
  images: ProductImage[];
  rating: number;
  description: string;
  endDate: string;
  inStock: boolean;
}

export function TodayHotDeal() {
  const { t } = useSettings();
  const cart = useCart();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState(false);
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  const handleAddToCart = () => {
    if (!product || !product.inStock) return;

    setAddingToCart(true);

    try {
      // Add to cart using the cart context
      cart.addToCart({
        id: parseInt(product.id, 10),
        name: product.name,
        price: product.discountedPrice,
        discountPrice: product.discountedPrice,
        image: product.images[0].url,
        originalPrice: product.price, // Use the regular price as original price
      }, 1, [], undefined);

      toast.success(`${product.name} added to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add product to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  useEffect(() => {
    const fetchHotDeal = async () => {
      setLoading(true);
      try {

        // Try to fetch from API first
        try {
          // Prepare parameters and headers according to API requirements
          const param = {
            "requestParameters": {
              "PageNo": 1,
              "PageSize": 1,
              "recordValueJson": "[]"
            }
          };

          const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          };

          // Import MakeApiCallAsync from api-helper
          const { MakeApiCallAsync } = await import('@/lib/api-helper');

          // Use the API helper to make the call with the exact endpoint from the screenshot
          const response = await MakeApiCallAsync(
            'api/v1/products/get-all-products', // Exact endpoint from the screenshot
            null,
            {
              requestParameters: {
                SearchTerm: "",
                CategoryID: null,
                TagID: null,
                ManufacturerID: null,
                MinPrice: null,
                MaxPrice: null,
                Rating: null,
                OrderByColumnName: "Price DESC", // Sort by price descending as shown in screenshot
                PageNo: 1,
                PageSize: 1,
                recordValueJson: "[]"
              }
            },
            headers,
            "POST",
            true
          );

          // Process the response
          if (response?.data?.data) {
            try {
              const parsedData = JSON.parse(response.data.data);
              console.log('Hot deal data:', parsedData);

              if (Array.isArray(parsedData) && parsedData.length > 0) {
                // Get the first hot deal
                const hotDealItem = parsedData[0];

                // Create a product object from the API data
                const hotDealProduct: Product = {
                  id: hotDealItem.ProductId?.toString() || '1',
                  name: hotDealItem.ProductName || 'Hot Deal Product',
                  slug: (hotDealItem.ProductName || 'hot-deal').toLowerCase().replace(/\s+/g, '-'),
                  price: hotDealItem.Price || 99.99,
                  discountedPrice: hotDealItem.DiscountedPrice || 79.99,
                  images: hotDealItem.ProductImagesJson?.map((img: any) => ({
                    id: img.AttachmentID?.toString() || '1',
                    url: img.AttachmentURL ? `${Config.ADMIN_BASE_URL}${img.AttachmentURL}` : '/images/placeholder.jpg',
                    alt: hotDealItem.ProductName || 'Hot deal product'
                  })) || [{ id: '1', url: '/images/placeholder.jpg', alt: 'Hot deal product' }],
                  rating: hotDealItem.Rating || 4.5,
                  description: hotDealItem.ShortDescription || 'Limited time offer on this amazing product!',
                  endDate: hotDealItem.DiscountEndDate || new Date(Date.now() + 86400000 * 3).toISOString(), // 3 days from now
                  inStock: (hotDealItem.StockQuantity || 0) > 0
                };

                setProduct(hotDealProduct);
                return; // Exit early if we successfully got data
              }
            } catch (parseError) {
              console.error('Error parsing hot deal data:', parseError);
              // Continue to fallback data
            }
          }
        } catch (apiError) {
          console.error('API call failed:', apiError);
          // Continue to fallback data
        }

        // If we reach here, either the API call failed or returned invalid data
        console.log('No hot deal data available from API');
        setProduct(null);

      } catch (error) {
        console.error('Error in fetchHotDeal:', error);
        // Don't use fallback data, just show empty state
        setProduct(null);
      } finally {
        setLoading(false);
      }
    };

    fetchHotDeal();
  }, []);

  useEffect(() => {
    if (!product) return;

    const calculateTimeLeft = () => {
      const difference = new Date(product.endDate).getTime() - new Date().getTime();

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        });
      }
    };

    const timer = setInterval(calculateTimeLeft, 1000);
    return () => clearInterval(timer);
  }, [product]);

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-6 w-6" />
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <Skeleton className="aspect-square" />
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-20 w-full" />
            <div className="grid grid-cols-4 gap-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton key={index} className="h-16 w-full" />
              ))}
            </div>
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </Card>
    );
  }

  if (!product) {
    return null;
  }

  const discount = Math.round(
    ((product.price - product.discountedPrice) / product.price) * 100
  );

  return (
    <Card className="p-6">
      <div className="flex items-center space-x-2 mb-4">
        <h2 className="text-2xl font-bold">Today's Hot Deal</h2>
        <Flame className="h-6 w-6 text-red-500" />
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden rounded-lg">
          <Image
            src={product.images[0].url}
            alt={product.images[0].alt}
            fill
            className="object-cover"
          />
          <Badge
            variant="destructive"
            className="absolute left-2 top-2"
          >
            -{discount}%
          </Badge>
        </div>

        {/* Product Details */}
        <div className="space-y-4">
          <Link
            href={`/product/${product.slug}`}
            className="block hover:text-primary"
          >
            <h3 className="text-2xl font-bold">{product.name}</h3>
          </Link>

          <ProductRatingStars rating={product.rating} />

          <p className="text-muted-foreground">{product.description}</p>

          {/* Countdown Timer */}
          <div className="grid grid-cols-4 gap-2 text-center">
            {Object.entries(timeLeft).map(([key, value]) => (
              <div
                key={key}
                className="bg-muted p-2 rounded-lg"
              >
                <div className="text-2xl font-bold">{value}</div>
                <div className="text-xs text-muted-foreground">{key}</div>
              </div>
            ))}
          </div>

          {/* Price */}
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-primary">
              ${product.discountedPrice.toFixed(2)}
            </span>
            <span className="text-lg text-muted-foreground line-through">
              ${product.price.toFixed(2)}
            </span>
          </div>

          {/* Add to Cart Button */}
          <Button
            size="lg"
            className="w-full"
            disabled={!product.inStock || addingToCart}
            onClick={handleAddToCart}
          >
            {addingToCart ? (
              <>
                <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
                Adding...
              </>
            ) : (
              <>
                <ShoppingBag className="mr-2 h-5 w-5" />
                {product.inStock ? 'Add to Cart' : 'Out of Stock'}
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
}