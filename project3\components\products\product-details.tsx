// components/products/product-details.tsx
"use client"
import { useState, useEffect, useMemo, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import axios from "axios"
import { 
  ShoppingCart, Heart, Share2, Star, ChevronRight, ChevronDown, ArrowLeft, Check,
  Truck, RotateCcw, Award, Clock
} from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { useCart } from "@/contexts/cart-context"
import { useWishlist } from "@/contexts/wishlist-context"
import { ProductSpecifications } from "@/components/products/product-specifications"
import { ProductMediaGallery } from "@/components/products/product-media-gallery"
import ProductLoading from "./product-loading"
import ProductError from "./product-error"

// Define the Product type based on the data structure we expect from the API
interface Product {
  ProductId: number
  ProductName: string
  FullDescription: string
  ShortDescription: string
  Price: number
  DiscountPrice: number
  StockQuantity: number
  OrderMinimumQuantity: number
  OrderMaximumQuantity: number
  DisplayStockQuantity: boolean
  MetaTitle: string
  MetaDescription: string
  MetaKeywords: string
  ProductImagesJson: Array<{
    AttachmentURL: string
  }>
  AttributesJson: Array<{
    ProductAttributeID: number
    AttributeValueID: number
    AttributeName: string
    AttributeValueText: string
    PriceAdjustment: number
    PriceAdjustmentType: number
  }>
}

interface ProductDetailsProps {
  productId: string
}

function ProductDetails({ productId }: ProductDetailsProps) {
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>('description')
  const [quantity, setQuantity] = useState<number>(1)
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, boolean>>({})
  const [addingToCart, setAddingToCart] = useState<boolean>(false)
  const [addingToWishlist, setAddingToWishlist] = useState<boolean>(false)
  
  const cart = useCart()
  const wishlist = useWishlist()
  const router = useRouter()

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true)
        const response = await axios.post(
          'https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail',
          {
            requestParameters: {
              ProductId: Number(productId),
              recordValueJson: "[]"
            }
          }
        )

        if (response.data && response.data.data) {
          const productData = JSON.parse(response.data.data)
          setProduct(Array.isArray(productData) ? productData[0] : productData)
        } else {
          throw new Error('Invalid product data received')
        }
      } catch (err) {
        console.error('Error fetching product:', err)
        setError('Failed to load product. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [productId])

  // Handle quantity changes
  const incrementQuantity = useCallback(() => {
    if (!product) return
    setQuantity(prev => {
      const maxQty = product.OrderMaximumQuantity > 0 
        ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) 
        : product.StockQuantity
      return Math.min(prev + 1, maxQty)
    })
  }, [product])

  const decrementQuantity = useCallback(() => {
    setQuantity(prev => Math.max(prev - 1, product?.OrderMinimumQuantity || 1))
  }, [product?.OrderMinimumQuantity])

  // Handle attribute selection
  const handleAttributeChange = useCallback((attrId: string, valueId: string) => {
    setSelectedAttributes(prev => ({
      ...prev,
      [`${attrId}_${valueId}`]: !prev[`${attrId}_${valueId}`]
    }))
  }, [])

  // Calculate adjusted price based on selected attributes
  const calculateAdjustedPrice = useCallback(() => {
    if (!product) return product?.Price || 0
    
    let adjustment = 0
    
    product.AttributesJson?.forEach(attr => {
      if (selectedAttributes[`${attr.ProductAttributeID}_${attr.AttributeValueID}`]) {
        if (attr.PriceAdjustmentType === 1) {
          // Fixed amount
          adjustment += attr.PriceAdjustment
        } else if (attr.PriceAdjustmentType === 2) {
          // Percentage
          adjustment += (product.Price * attr.PriceAdjustment) / 100
        }
      }
    })
    
    return (product.DiscountPrice || product.Price) + adjustment
  }, [product, selectedAttributes])

  // Render price with currency formatting
  const renderPrice = useCallback((price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }, [])

  // Handle add to cart
  const handleAddToCart = useCallback(() => {
    if (!product) return
    
    setAddingToCart(true)
    try {
      const productImage = product.ProductImagesJson?.[0]?.AttachmentURL 
        ? `https://admin.codemedicalapps.com${product.ProductImagesJson[0].AttachmentURL}`
        : '/placeholder.jpg'

      const selectedAttrs = (product.AttributesJson || []).filter(attr => 
        selectedAttributes[`${attr.ProductAttributeID}_${attr.AttributeValueID}`]
      )

      cart.addToCart(
        {
          id: product.ProductId,
          name: product.ProductName,
          price: calculateAdjustedPrice(),
          discountPrice: product.DiscountPrice,
          image: productImage,
          originalPrice: product.Price,
        },
        quantity,
        selectedAttrs
      )

      toast.success(`${quantity} × ${product.ProductName} added to your cart`)
    } catch (err) {
      console.error('Error adding to cart:', err)
      toast.error('Failed to add product to cart. Please try again.')
    } finally {
      setAddingToCart(false)
    }
  }, [product, quantity, selectedAttributes, cart, calculateAdjustedPrice])

  // Handle add to wishlist
  const handleAddToWishlist = useCallback(() => {
    if (!product) return
    
    setAddingToWishlist(true)
    try {
      if (wishlist.isInWishlist(product.ProductId)) {
        wishlist.removeFromWishlist(product.ProductId)
        toast.success(`${product.ProductName} removed from wishlist`)
      } else {
        wishlist.addToWishlist(product.ProductId)
        toast.success(`${product.ProductName} added to wishlist`)
      }
    } catch (err) {
      console.error('Error updating wishlist:', err)
      toast.error('Failed to update wishlist. Please try again.')
    } finally {
      setAddingToWishlist(false)
    }
  }, [product, wishlist])

  // Handle share
  const handleShare = useCallback(() => {
    if (typeof window === 'undefined' || !product) return
    
    if (navigator.share) {
      navigator.share({
        title: product.MetaTitle || product.ProductName,
        text: product.MetaDescription || `Check out ${product.ProductName}`,
        url: window.location.href,
      }).catch(console.error)
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link copied to clipboard')
    }
  }, [product])

  // Show loading state
  if (loading) {
    return <ProductLoading />
  }

  // Show error state
  if (error || !product) {
    return <ProductError error={error || 'Product not found'} retry={() => window.location.reload()} />
  }

  // Calculate stock percentage for the indicator
  const stockPercentage = product.DisplayStockQuantity && product.StockQuantity > 0 
    ? Math.min(100, (quantity / product.StockQuantity) * 100) 
    : 0

  const currentPrice = calculateAdjustedPrice()
  const hasDiscount = product.DiscountPrice && product.DiscountPrice < product.Price
  const isInWishlist = wishlist.isInWishlist(product.ProductId)

  return (
    <div className="container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/products">Products</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{product.ProductName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Product Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Image Gallery */}
        <div className="sticky top-4">
          <ProductMediaGallery 
            images={product.ProductImagesJson?.map(img => ({
              src: `https://admin.codemedicalapps.com${img.AttachmentURL}`,
              alt: product.ProductName
            })) || []}
          />
        </div>

        {/* Right Column - Product Info */}
        <div>
          <div className="mb-6">
            <h1 className="text-3xl font-bold mb-2">{product.ProductName}</h1>
            
            {/* Price */}
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl font-bold text-primary">
                {renderPrice(currentPrice)}
              </span>
              {hasDiscount && (
                <>
                  <span className="text-lg line-through text-gray-500">
                    {renderPrice(product.Price)}
                  </span>
                  <Badge variant="destructive" className="text-sm">
                    {Math.round(((product.Price - product.DiscountPrice) / product.Price) * 100)}% OFF
                  </Badge>
                </>
              )}
            </div>

            {/* Stock Status */}
            {product.DisplayStockQuantity && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">
                    {product.StockQuantity > 0 
                      ? `In Stock (${product.StockQuantity} available)` 
                      : 'Out of Stock'}
                  </span>
                  {product.StockQuantity > 0 && (
                    <span className="text-sm font-medium">{quantity} of {product.StockQuantity}</span>
                  )}
                </div>
                {product.StockQuantity > 0 && (
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-green-500 h-2.5 rounded-full" 
                      style={{ width: `${stockPercentage}%` }}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Short Description */}
            {product.ShortDescription && (
              <div 
                className="prose max-w-none mb-6 text-gray-700"
                dangerouslySetInnerHTML={{ __html: product.ShortDescription }}
              />
            )}

            {/* Attributes */}
            {product.AttributesJson && product.AttributesJson.length > 0 && (
              <div className="space-y-4 mb-6">
                {Object.entries(
                  product.AttributesJson.reduce<Record<string, typeof product.AttributesJson>>((acc, attr) => {
                    if (!acc[attr.AttributeName]) {
                      acc[attr.AttributeName] = [];
                    }
                    acc[attr.AttributeName].push(attr);
                    return acc;
                  }, {})
                ).map(([attrName, attributes]) => (
                  <div key={attrName}>
                    <h4 className="font-medium mb-2">{attrName}</h4>
                    <div className="flex flex-wrap gap-2">
                      {attributes.map((attr) => {
                        const isSelected = selectedAttributes[`${attr.ProductAttributeID}_${attr.AttributeValueID}`];
                        return (
                          <Button
                            key={`${attr.ProductAttributeID}_${attr.AttributeValueID}`}
                            variant={isSelected ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handleAttributeChange(
                              attr.ProductAttributeID.toString(),
                              attr.AttributeValueID.toString()
                            )}
                            className="flex items-center gap-1"
                          >
                            {isSelected && <Check size={14} />}
                            {attr.AttributeValueText}
                            {attr.PriceAdjustment !== 0 && (
                              <span className="text-xs ml-1">
                                ({attr.PriceAdjustmentType === 1 ? '+' : ''}
                                {attr.PriceAdjustmentType === 2 ? '+' : ''}
                                {renderPrice(attr.PriceAdjustment)}
                                {attr.PriceAdjustmentType === 2 ? '%' : ''})
                              </span>
                            )}
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Quantity and Add to Cart */}
            <div className="flex flex-wrap items-center gap-4 mb-6">
              <div className="flex items-center border rounded-md">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={decrementQuantity}
                  disabled={quantity <= (product.OrderMinimumQuantity || 1)}
                  className="h-10 w-10"
                >
                  -
                </Button>
                <span className="w-12 text-center">{quantity}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={incrementQuantity}
                  disabled={quantity >= product.StockQuantity}
                  className="h-10 w-10"
                >
                  +
                </Button>
              </div>
              
              <Button 
                size="lg" 
                className="flex-1 min-w-[200px]"
                onClick={handleAddToCart}
                disabled={product.StockQuantity <= 0 || addingToCart}
              >
                <ShoppingCart className="mr-2 h-4 w-4" />
                {addingToCart ? 'Adding...' : 'Add to Cart'}
              </Button>

              <Button 
                variant="outline" 
                size="icon" 
                className="h-11 w-11"
                onClick={handleAddToWishlist}
                disabled={addingToWishlist}
              >
                <Heart 
                  className={`h-5 w-5 ${isInWishlist ? 'fill-current text-red-500' : ''}`} 
                />
              </Button>

              <Button 
                variant="outline" 
                size="icon" 
                className="h-11 w-11"
                onClick={handleShare}
              >
                <Share2 className="h-5 w-5" />
              </Button>
            </div>

            {/* Product Meta */}
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4 text-gray-400" />
                <span>Free shipping on orders over $50</span>
              </div>
              <div className="flex items-center gap-2">
                <RotateCcw className="h-4 w-4 text-gray-400" />
                <span>30-day return policy</span>
              </div>
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4 text-gray-400" />
                <span>Authentic products</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <span>Fast shipping</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Tabs */}
      <div className="mt-16">
        <Tabs 
          defaultValue="description" 
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2">
            <TabsTrigger value="description">Description</TabsTrigger>
            <TabsTrigger value="specifications">Specifications</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="shipping">Shipping & Returns</TabsTrigger>
          </TabsList>
          <TabsContent value="description" className="mt-4 p-8 bg-white rounded-lg shadow-sm">
            {product.FullDescription ? (
              <div 
                className="prose max-w-none" 
                dangerouslySetInnerHTML={{ __html: product.FullDescription }} 
              />
            ) : (
              <p className="text-gray-500 italic">No description available.</p>
            )}
          </TabsContent>
          <TabsContent value="specifications" className="mt-4 p-8 bg-white rounded-lg shadow-sm">
            {product.AttributesJson && product.AttributesJson.length > 0 ? (
              <ProductSpecifications 
                attributes={product.AttributesJson} 
                className="bg-white rounded-lg"
              />
            ) : (
              <p className="text-gray-500 italic">No specifications available.</p>
            )}
          </TabsContent>
          <TabsContent value="reviews" className="mt-4 p-8 bg-white rounded-lg shadow-sm">
            {/* Reviews content */}
          </TabsContent>
          <TabsContent value="shipping" className="mt-4 p-8 bg-white rounded-lg shadow-sm">
            {/* Shipping content */}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default function ProductPage() {
  const params = useParams();
  const id = params.id as string;
  if (!id) {
    return <ProductError error="Product ID not found" retry={() => {}} />
  }
  return <ProductDetails productId={id} />
}