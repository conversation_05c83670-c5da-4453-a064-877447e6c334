Stack trace:
Frame         Function      Args
0007FFFF91E0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF80E0) msys-2.0.dll+0x1FE8E
0007FFFF91E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF94B8) msys-2.0.dll+0x67F9
0007FFFF91E0  000210046832 (000210286019, 0007FFFF9098, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF91E0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF91E0  000210068E24 (0007FFFF91F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF94C0  00021006A225 (0007FFFF91F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC7C020000 ntdll.dll
7FFC7BCD0000 KERNEL32.DLL
7FFC79210000 KERNELBASE.dll
7FFC75B40000 apphelp.dll
7FFC7B270000 USER32.dll
7FFC798C0000 win32u.dll
7FFC7ADB0000 GDI32.dll
7FFC79600000 gdi32full.dll
7FFC79A40000 msvcp_win.dll
7FFC79C70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC7A690000 advapi32.dll
7FFC7BE10000 msvcrt.dll
7FFC7A780000 sechost.dll
7FFC7BEC0000 RPCRT4.dll
7FFC78680000 CRYPTBASE.DLL
7FFC79170000 bcryptPrimitives.dll
7FFC7B230000 IMM32.DLL
