﻿@model Entities.MainModels.ProductsCatalogModel

@{
    #region page basic info
    ViewData["Title"] = Model?.PageBasicInfoObj?.PageTitle ?? "";
    ViewData["EntityId"] = Model?.PageBasicInfoObj?.EntityId ?? 0;

    #endregion
}


<!--Page specific java script-->
<script src="~/content/themeContent/global_assets/js/demo_pages/form_checkboxes_radios.js"></script>
<script src="~/content/themeContent/global_assets/js/plugins/uploaders/fileinput/plugins/purify.min.js"></script>
<script src="~/content/themeContent/global_assets/js/plugins/uploaders/fileinput/plugins/sortable.min.js"></script>
<script src="~/content/themeContent/global_assets/js/plugins/uploaders/fileinput/fileinput.min.js"></script>
<script src="~/content/themeContent/global_assets/js/demo_pages/uploader_bootstrap.js"></script>
<!--/Page specific java script-->
@{
    List<SelectListItem> ActiveInactiveStatus = new List<SelectListItem>();
    ActiveInactiveStatus.Add(new SelectListItem { Value = "true", Text = "Active" });
    ActiveInactiveStatus.Add(new SelectListItem { Value = "false", Text = "In Active" });

    Dictionary<string, string>? IsActiveDropDown = new Dictionary<string, string>();
    IsActiveDropDown = ActiveInactiveStatus.ToDictionary(v => v.Value, t => t.Text);


}

<!-- Page header -->
@{
    PageHeader pageHeader = new PageHeader
            {
                PageTitle = Model?.PageBasicInfoObj?.PageTitle ?? "Page Info",
                ShowAddNewButton = false,
                ShowActionsButton = false,
                ShowExportToPdfButton = false,
                ShowExportToExcelButton = false,
                ShowGoBackButton = true
            };

}
@await Html.PartialAsync("~/Views/Common/_PageHeader.cshtml", pageHeader)
<!-- /page header -->



<div class="content">


    <!-- Error Area -->
    <div id="error-messages-area">
        @{
            SuccessErrorMsgEntity? successErrorMsgEntity = new SuccessErrorMsgEntity();
            successErrorMsgEntity = Model.SuccessErrorMsgEntityObj == null ? new SuccessErrorMsgEntity() : Model.SuccessErrorMsgEntityObj;
        }

        @await Html.PartialAsync("~/Views/Common/_SuccessErrorMsg.cshtml", successErrorMsgEntity)
    </div>
    <!-- /Error Area -->

    <form class="form-validate-jquery" id="data-insert-form" action="#">


        <div class="card border-left-3 border-left-slate">
            <div class="card-header header-elements-inline">
                <h6 class="card-title" id="lbl_prd_new_page_sub_title">@(Model?.PageBasicInfoObj?.PageTitle ?? "Add New Product")</h6>
                <div class="header-elements">
                    <div class="list-icons">
                        <a class="list-icons-item" data-action="collapse"></a>
                        @*  <a class="list-icons-item" data-action="reload"></a>
                        <a class="list-icons-item" data-action="remove"></a>*@
                    </div>
                </div>
            </div>

            <div class="card-body">
                <ul class="nav nav-tabs nav-tabs-highlight mb-0">
                    <li class="nav-item"><a href="#bordered-tab1" class="nav-link active" data-toggle="tab"><i class="icon-info3 mr-2"></i><span id="lbl_prd_info_tab">Product Basic Info</span> </a></li>
                    <li class="nav-item"><a href="#bordered-tab2" class="nav-link" data-toggle="tab"><i class="icon-price-tag3 mr-2"></i><span id="lbl_prd_price_tab">Prices</span> </a></li>
                    <li class="nav-item"><a href="#bordered-tab3" class="nav-link" data-toggle="tab"><i class="fas fa-shipping-fast mr-2"></i><span id="lbl_prd_shipping_tab">Shipping</span> </a></li>
                    <li class="nav-item" style="display:none"><a href="#bordered-tab4" class="nav-link" data-toggle="tab"><i class="fas fa-box mr-2"></i> <span id="lbl_prd_inventory_tab">Inventory</span></a></li>
                    <li class="nav-item"><a href="#bordered-tab5" class="nav-link" data-toggle="tab"><i class="icon-file-picture mr-2"></i><span id="lbl_prd_pictures_tab">Pictures And Video</span> </a></li>
                    <li class="nav-item"><a href="#bordered-tab6" class="nav-link" data-toggle="tab"><i class="icon-browser mr-2"></i><span id="lbl_prd_seo_tab">SEO</span> </a></li>
                    <li class="nav-item"><a href="#bordered-tab7" class="nav-link" data-toggle="tab"><i class="icon-display4 mr-2"></i> <span id="lbl_prd_attributes_tab">Product Attributes</span></a></li>

                </ul>

                <div class="tab-content card card-body border-top-0 rounded-top-0 mb-0">
                    <div class="tab-pane fade show active" id="bordered-tab1">
                        <fieldset class="mb-3">

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">
                                    <span id="lbl_prd_new_product_name">Product Name</span>

                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Enter here the name of the product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="text" maxlength="700" name="ProductName" id="ProductName" class="form-control" required placeholder="">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_short_description">Short Description</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Enter here short description for product if necessary"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <textarea class="form-control" maxlength="1500" name="ShortDescription" id="ShortDescription" rows="5" cols="5"></textarea>

                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_full_description">Full Description</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Enter here full description for the product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <textarea class="form-control" id="FullDescription" name="FullDescription" required rows="4" cols="5"></textarea>

                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_manufacturer">Product Types</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select Product Types from the dropdown"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="producttypeId" name="producttypeId" data-placeholder="Select a Product Types..." class="form-control">
                                        <option value="">Select a Product Types...</option>

                                        @{
                                            if (Model != null && Model.producttypes != null && Model.producttypes.Count > 0)
                                            {
                                                foreach (var item in Model.producttypes)
                                                {
                                                    <option value="@item.producttypeId">@item.Name</option>
                                                }
                                            }
                                        }



                                    </select>
                                </div>

                            </div>
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_manufacturer">Manufacturer</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select manufacturer from the dropdown"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="ManufacturerId" name="ManufacturerId" data-placeholder="Select a Manufacturer..." class="form-control">
                                        <option value="">Select a Manufacturer...</option>

                                        @{
                                            if (Model != null && Model.ManufacturerList != null && Model.ManufacturerList.Count > 0)
                                            {
                                                foreach (var item in Model.ManufacturerList)
                                                {
                                                    <option value="@item.ManufacturerId">@item.Name</option>
                                                }
                                            }
                                        }



                                    </select>
                                </div>

                            </div>
                            @if (ViewBag.LoginUserId==null){
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_vendor">Vendor</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select vendor name from the drop down"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="VendorId" name="VendorId" data-placeholder="Select a Vendor..." class="form-control" required>
                                        <option value="">Select a Vendor...</option>

                                        @{
                                            if (Model != null && Model.UsersList != null && Model.UsersList.Count > 0)
                                            {
                                                foreach (var item in Model.UsersList)
                                                {
                                                    string FullName = item.FirstName + " " + item.LastName;
                                 bool isSelected = item.UserId == (int?)ViewBag.LoginUserId; // Compare with ViewBag
                                                    if (isSelected)
                                                    {
                                                        <option value="@item.UserId" selected="selected">@FullName</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="@item.UserId">@FullName</option>
                                                    }
          
             
                                                }
                                            }
                                        }

                                    </select>
                                </div>
                            </div>}else{
                                <div class="form-group row d-none">
                                    <label class="col-form-label col-lg-3">

                                        <span id="lbl_prd_new_vendor">Vendor</span>
                                        <span class="text-danger">*</span>
                                        <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select vendor name from the drop down"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                    </label>
                                    <div class="col-lg-9">
                                        <select id="VendorId" name="VendorId" data-placeholder="Select a Vendor..." class="form-control" required>
                                            <option value="">Select a Vendor...</option>

                                            @{
                                                if (Model != null && Model.UsersList != null && Model.UsersList.Count > 0)
                                                {
                                                    foreach (var item in Model.UsersList)
                                                    {
                                                        string FullName = item.FirstName + " " + item.LastName;
                                                        bool isSelected = item.UserId == (int?)ViewBag.LoginUserId; // Compare with ViewBag
                                                        if (isSelected)
                                                        {
                                                            <option value="@item.UserId" selected="selected">@FullName</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="@item.UserId">@FullName</option>
                                                        }


                                                    }
                                                }
                                            }

                                        </select>
                                    </div>
                                </div>

                            }

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_categories">Categories</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select categories for the product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="SelectedCategoryIds" name="SelectedCategoryIds" multiple="multiple" class="form-control select" data-fouc required>

                                        @{
                                            if (Model != null && Model.CategoryList != null && Model.CategoryList.Count > 0)
                                            {
                                                foreach (var item in Model.CategoryList)
                                                {
                                                    string? ParentCategory = Model.CategoryList.Where(c => c.CategoryId == item.ParentCategoryId).Select(c => c.Name).FirstOrDefault();
                                                    string? Category = String.IsNullOrWhiteSpace(ParentCategory) ? item.Name : ParentCategory + " >> " + item.Name;

                                                    <option value="@item.CategoryId">@Category</option>
                                                }
                                            }
                                        }


                                    </select>
                                </div>

                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_tags">Tags</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Type and search tags from the drop down if you want to associate tags with this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="SelectedTagIds" name="SelectedTagIds" class="form-control select-multiple-limited" multiple="multiple" data-fouc>
                                    </select>
                                </div>

                            </div>



                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_isactive">IsActive</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="If this option is uncheck then product will not be visible on website. It is only for showing/hiding product on website at later stages"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsActive" name="IsActive" class="form-check-input-styled-info" checked data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_showOnHomePage"> Show on home page</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="In every website, there is home page. If you want to show this product on the home page of your website, then make this option enable."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="ShowOnHomePage" name="ShowOnHomePage" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_markAsNew">  Mark as new</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Check to mark the product as new. Use this option for promoting new products."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="MarkAsNew" name="MarkAsNew" class="form-check-input-styled-info" checked data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_allowCustomerReview">Allow customer reviews</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Check this option if you want to customers to review this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="AllowCustomerReviews" name="AllowCustomerReviews" class="form-check-input-styled-info" checked data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_availableStartDte">Available Start Date</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Product will be available from this date"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-prepend">
                                                <span class="input-group-text"><i class="icon-calendar22 mr-2"></i></span>
                                            </span>
                                            <input type="text" id="SellStartDatetimeUtc" name="SellStartDatetimeUtc" class="form-control date-filter-exclude pickadate" value="Start date" placeholder="Start date &hellip;">
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_availableEndDte"> Available End Date</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Product will not be available after this date"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-prepend">
                                                <span class="input-group-text"><i class="icon-calendar22 mr-2"></i></span>
                                            </span>
                                            <input type="text" id="SellEndDatetimeUtc" name="SellEndDatetimeUtc" class="form-control date-filter-exclude pickadate" value="End date" placeholder="End date &hellip;">
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_sku"> Sku</span>
                                    @* <span class="text-danger">*</span>*@
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="A unique product stock keeping unit(Sku) value"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="text" maxlength="1000" id="Sku" name="Sku" class="form-control" placeholder="">
                                </div>
                            </div>

                        </fieldset>
                    </div>

                    <div class="tab-pane fade" id="bordered-tab2">
                        <fieldset class="mb-3">

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_price"> Price</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Enter current price for the product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">

                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-dollar-sign mr-2"></i></span>
                                        </div>
                                        <input type="number" min="0" max="100000000" id="Price" name="Price" class="form-control" required placeholder="Enter product price">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_oldPrice"> Old Price</span>
                                    @*   <span class="text-danger">*</span>*@
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="It is the old price of the product. If you want to compare current price the old price then use this field"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">

                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-dollar-sign mr-2"></i></span>
                                        </div>
                                        <input type="number" min="0" max="100000000" id="OldPrice" name="OldPrice" class="form-control" placeholder="Enter product old price if any">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="Point"> Point</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Enter current Point for the product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">

                                    <div class="input-group">
                                     
                                        <input type="number" min="0" max="100000000" id="Pointno" name="Pointno" class="form-control" required placeholder="Enter product Point">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_discountAllowed"> Is Discount Allowed?</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="If this option is uncheck then no discount will be allowed on this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsDiscountAllowed" name="IsDiscountAllowed" onchange="EnableDiscountsDropDown();" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row" id="SelectedDiscountIdsDiv" style="display: none;">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_discounts"> Discounts</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Drop down of discounts types that assigned to product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="SelectedDiscountIds" name="SelectedDiscountIds" class="form-control select" data-fouc>
                                        <option value="">Select discount</option>
                                        @{
                                            if (Model != null && Model.DiscountsList != null && Model.DiscountsList.Count > 0)
                                            {
                                                foreach (var item in Model.DiscountsList)
                                                {

                                                    <option value="@item.DiscountId">@item.Title</option>
                                                }
                                            }
                                        }




                                    </select>
                                </div>
                            </div>


                        </fieldset>
                    </div>

                    <div class="tab-pane fade" id="bordered-tab3">
                        <fieldset class="mb-3">



                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_freeShipping">  Free Shipping</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Check this option if this product coming with free shipping"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsShippingFree" name="IsShippingFree" onchange="EnableShippingChargesBox();" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row" id="ShippingChargesDiv">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_shippingCharges"> Shipping Charges</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Shipping charges associated with this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="number" min="0" id="ShippingCharges" name="ShippingCharges" class="form-control" data-fouc>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_shippingMethods">  Shipping Methods</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Different shipping methods"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="SelectedShippingMethodIds" name="SelectedShippingMethodIds" multiple="multiple" class="form-control select" data-fouc required>

                                        @{
                                            if (Model != null && Model.ShippingMethodsList != null && Model.ShippingMethodsList.Count > 0)
                                            {
                                                foreach (var item in Model.ShippingMethodsList)
                                                {
                                                    <option value="@item.ShippingMethodId">@item.MethodName</option>
                                                }
                                            }
                                        }


                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_estimatedShippingDays">  Estimated Shipping Day</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select estimated shipping days from the drop down that the process may take during delivery of this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="EstimatedShippingDays" name="EstimatedShippingDays" class="form-control" required>

                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>


                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_returnAble">  Return Able</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="If this option is uncheck, then customer will not be able to put request for returing this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsReturnAble" name="IsReturnAble" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>


                        </fieldset>
                    </div>
                    <div class="tab-pane fade" id="bordered-tab4">
                        <fieldset class="mb-3">
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_inventoryMethod">  Inventory Method</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Inventory method"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="InventoryMethodID" name="InventoryMethodID" class="form-control">
                                        <option value="">Select Inventory Method...</option>
                                        @{
                                            if (Model != null && Model.InventoryMethodsList != null && Model.InventoryMethodsList.Count > 0)
                                            {
                                                foreach (var item in Model.InventoryMethodsList)
                                                {
                                                    <option value="@item.InventoryMethodId">@item.InventoryMethodName</option>
                                                }
                                            }
                                        }



                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_wareHouse"> Warehouse</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Warehouse"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="WarehouseId" name="WarehouseId" class="form-control">
                                        <option value="">Select Warehouse...</option>
                                        @{
                                            if (Model != null && Model.WarehousesList != null && Model.WarehousesList.Count > 0)
                                            {
                                                foreach (var item in Model.WarehousesList)
                                                {
                                                    <option value="@item.WarehouseId">@item.WarehouseName</option>
                                                }
                                            }
                                        }


                                    </select>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_stockQuantity"> Stock Quantity</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="You can set here the stock quantity. The default value is 100"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="number" min="0" id="StockQuantity" name="StockQuantity" value="100" class="form-control" required />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_bountToStockQuanity"> Bound To Stock Quantity</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="If this option is checked, then product will be be bound to the stock quantity."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsBoundToStockQuantity" name="IsBoundToStockQuantity" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_displayStockQuanity">  Display Stock Quantity</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Check to display stock quantity. When enabled, customers will see stock quantity."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="DisplayStockQuantity" name="DisplayStockQuantity" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_orderMinQuantity">Order Minimum Cart Quantity</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Set the minimum quantity allowed in a customer's shopping cart e.g. set to 4 to only allow customers to purchase 4 or more of this product."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="number" min="1" id="OrderMinimumQuantity" name="OrderMinimumQuantity" class="form-control" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_orderMaxQuantity"> Order Maximum Cart Quantity</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Set the maximum quantity allowed in a customer's shopping cart e.g. set to 4 to only allow customers to purchase 4 of this product."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="number" min="1" id="OrderMaximumQuantity" name="OrderMaximumQuantity" class="form-control" />
                                </div>
                            </div>


                        </fieldset>
                    </div>
                    <div class="tab-pane fade" id="bordered-tab5">
                        <fieldset class="mb-3">


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_productImage">  Product Images</span>
                                    <span class="text-danger">*</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select any category from the dropdown"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="file" id="ProductImages" name="ProductImages" class="file-input" multiple="multiple" data-show-upload="false" data-show-caption="true" data-show-preview="true" data-fouc>

                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">
                                    <span id="lbl_prd_update_videofile">  Product Video</span>


                                </label>
                                <div class="col-lg-9">
                                    <input type="file" id="videofile" name="videofile" class="file-input" multiple="multiple" data-show-upload="false" data-show-caption="true" data-show-preview="true" data-fouc>

                                </div>
                            </div>
                            <div class="form-group row" style="display:none">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_isdigital">  Is Digital Product</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Check this option if this product is digital product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input type="checkbox" id="IsDigitalProduct" name="IsDigitalProduct" onchange="EnableDigitalProductBox();" class="form-check-input-styled-info" data-fouc>

                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row DownloadUrlOptionDiv" style="display: none;">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_downloadurloption">  Download Url Option</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Select either existing URL or add new file"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="DownloadUrlOption" name="DownloadUrlOption" class="form-control" onchange="EnableDigitalProdFileUploadDiv();">
                                        <option value="" selected>Select URL Option</option>
                                        <option value="1">Existing URL</option>
                                        <option value="2">New File Upload</option>


                                    </select>
                                </div>
                            </div>

                            <div class="form-group row" style="display: none;" id="DigitalProductExistingUrlDiv">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_existingUrl">  Existing Url</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Give here existing URL so that customer donwload the digital product from this link"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input id="DigitalProductExistingUrl" name="DigitalProductExistingUrl" class="form-control" type="text" />

                                </div>
                            </div>

                            <div class="form-group row" style="display: none;" id="DigitalProductNewFileUploadDiv">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_newfileupload">  New File Upload</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Upload new file"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input id="DigitalProductNewFileUpload" name="DigitalProductNewFileUpload" class="form-control" type="file" />

                                </div>
                            </div>


                        </fieldset>
                    </div>

                    <div class="tab-pane fade" id="bordered-tab6">

                        <fieldset class="mb-3">


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_metaTitle">   Meta Title</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="SEO meta title for this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="text" id="MetaTitle" name="MetaTitle" class="form-control" />
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_metaKeyWords">   Meta keywords</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Add here comma seperated keywords for SEO purpose of this product"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="text" id="MetaKeywords" name="MetaKeywords" class="form-control" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_metaDesc">   Meta Description</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Meta description for SEO"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">

                                    <textarea class="form-control" id="MetaDescription" name="MetaDescription" rows="5" cols="5"></textarea>
                                </div>
                            </div>

                        </fieldset>
                    </div>



                    <div class="tab-pane fade" id="bordered-tab7">
                        <fieldset class="mb-3">

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_attribute">  Attribute</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Choose an attribute from the dropdown"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="ProductAttributeId" class="form-control" onchange="GetProductAttributeValuesByAttributeID()">
                                        <option value="">Select an Attribute...</option>
                                        @{
                                            if (Model != null && Model.ProductAttributesList != null && Model.ProductAttributesList.Count > 0)
                                            {
                                                foreach (var item in Model.ProductAttributesList)
                                                {
                                                    <option value="@item.ProductAttributeId">@item.DisplayName</option>
                                                }
                                            }
                                        }


                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_priceAdjustmentType">   Price Adjustment Type</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Determines whether to apply a percentage to the product. If not enabled, a fixed value is will be applied to the product cost."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="PriceAdjustmentType" name="PriceAdjustmentType" class="form-control">

                                        <option value="@((short)PriceAdjustmentTypeEnum.FixedValue)">Fixed Value</option>
                                        <option value="@((short)PriceAdjustmentTypeEnum.Percentage)">Percentage</option>


                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_priceAdjustment">   Price adjustment</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="The price adjustment applied when choosing this attribute value. For example '10' to add 10 dollars. Or 10% if 'Use percentage' is ticked. Its default value is zero"><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <input type="number" id="PriceAdjustment" name="PriceAdjustment" min="0" value="0" class="form-control" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-form-label col-lg-3">

                                    <span id="lbl_prd_new_attributeValue">   Attribute Value</span>
                                    <span data-popup="tooltip" title="" data-placement="right" data-original-title="Value of the attribute."><i class="icon-question4 ml-1 form-tooltip-color"></i> </span>
                                </label>
                                <div class="col-lg-9">
                                    <select id="AttributeValue" class="form-control">

                                        <option value="">Select an Attribute value...</option>

                                    </select>
                                </div>
                            </div>


                            <div class="form-group">
                                <div class="col-lg-12">

                                    <div class="d-flex justify-content-end align-items-center">
                                        <button type="button" onclick="AddProductAttributeRow();" class="btn bg-purple-300 ml-3" id="lbl_add_attribute_btn">Add Attribute</button>
                                    </div>
                                </div>
                            </div>


                            <div class=" row">
                                <div class="col-lg-12">
                                    <div class="table-responsive" id="product_attribute_data_table">
                                        <table class="table site-table-listing" id="product_attributes_table">
                                            <thead>
                                                <tr>
                                                    <th id="lbl_hdr_attribName"> Attribute</th>
                                                    <th id="lbl_hdr_attribValue"> Attribute Value</th>
                                                    <th id="lbl_hdr_attribPriceAdjType">Price Adjustment Type</th>
                                                    <th id="lbl_hdr_attribPriceAdj">Price Adjustment</th>



                                                    <th class="text-center" style="width: 20px;"><i class="icon-arrow-down12"></i></th>
                                                </tr>
                                            </thead>
                                            <tbody>


                                                <tr id="product_attribute_no_data_row">
                                                    <td class="text-center" colspan="20"><b>  No record found </b></td>

                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                            </div>


                        </fieldset>
                    </div>

                </div>
            </div>
        </div>



        <div class="row">
            <div class="col-lg-12">

                <div class="d-flex justify-content-end align-items-center">
                    <button type="reset" onclick="resetAnyFormById('data-insert-form');" class="btn btn-light" id="reset"><span id="lbl_prd_form_reset_btn">Reset</span>  <i class="icon-reload-alt ml-2"></i></button>
                    <button type="submit" onclick="SaveFormRecord();" class="btn btn-primary ml-3"> <span id="lbl_prd_form_save_btn">Save</span>  <i class="icon-paperplane ml-2"></i></button>
                </div>
            </div>
        </div>

    </form>




</div>
@section  Scripts{

    <!-- TinyMCE Plugin -->
    <script type="text/javascript" src="~/content/themeContent/global_assets/js/plugins/editors/tinymce/tinymce.min.js"></script>

    <script type="text/javascript">
        tinymce.init({
            selector: '#FullDescription',
            menubar: false,   //--menu bar should be false
            height: 350,    //--height of text editor
            // skin: "oxide-dark", //--for dark them, if no need of dark them then remove this attribute
            //  content_css: "dark", //--for dark theme, if no need of dark them then remove this attribute
            branding: false, //--remove bottom logo (Powered by tiny)

            //--basic plugins
            plugins: [
                'advlist  lists autolink link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],

            //--toolbar option like undo, text color setting, text background color setting etc
            toolbar: 'undo redo | formatselect fontselect fontsizeselect | ' +
                'image media link | bold italic underline | backcolor forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | casechange | ' +
                'removeformat | preview | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:12px }'


            //setup: function (editor) {
            //    editor.on('change', function (e) {
            //        editor.save();
            //    });
            //}
        });

        //For setting content of text area
        // tinymce.get("Specification_1Update").setContent(htmlSepicificaton1Update)


    </script>



    <script>

        // ✅ This will get data drop down for tags from data base
        $(document).ready(function () {
            $("#SelectedTagIds").select2({
                //tags: true,
                //multiple: true,
                //tokenSeparators: [',', ' '],
                minimumInputLength: 2,
                minimumResultsForSearch: 10,
                ajax: {
                    url: '@Url.Action("GetTagsListByKeyword","ProductsCatalog" , new { langCode = Model?.PageBasicInfoObj?.langCode })',
                    dataType: "json",
                    type: "GET",
                    delay: 250,
                    data: function (params) {

                        var queryParameters = {
                            term: params.term
                        }
                        return queryParameters;
                    },
                    processResults: function (data) {
                        debugger
                        return {
                            results: data.results
                        };
                    },
                    cache: true
                }
            });
        });





        function SaveFormRecord() {

            debugger

            // ✅ Trigger Tiny MCE Save first of all so that it associate required field values with all text areas
            tinymce.triggerSave()

            // ✅ On formt submit,fire event prevent
            $("#data-insert-form").submit(function (e) {
                e.preventDefault();
            });

            // ✅ Check if form is valid
            if (!$("#data-insert-form").valid()) {
                event.preventDefault();
                showSuccessErrorMsg("error", "Error", "Please fill all required fields!");
                return false;
            }

            // ✅ Check the validation of form in a new additional way
            if (!validateInsertForm("data-insert-form")) {
                event.preventDefault();
                showSuccessErrorMsg("error", "Error", "Please fill all required fields!");
                return false;
            }

            //-- #region All fields values getting area starts here
            let ProductName = $("#ProductName").val();
            let ShortDescription = $("#ShortDescription").val().trim();
            let FullDescription = tinymce.get("FullDescription").getContent();  //--do not add "#" sign here in case of tiny mce with input id
            let ManufacturerId = $("#ManufacturerId").val();
               let producttypeId = $("#producttypeId").val();
            let VendorId = $("#VendorId").val();
            let IsActive = ($('#IsActive').is(":checked") == true) ? true : false;
            let ShowOnHomePage = ($('#ShowOnHomePage').is(":checked") == true) ? true : false;
            let MarkAsNew = ($('#MarkAsNew').is(":checked") == true) ? true : false;
            let AllowCustomerReviews = ($('#AllowCustomerReviews').is(":checked") == true) ? true : false;
            let SellStartDatetimeUtc = $("#SellStartDatetimeUtc").val();
            let SellEndDatetimeUtc = $("#SellEndDatetimeUtc").val();
            let Sku = $("#Sku").val();
            let Price = $("#Price").val();
            let OldPrice = $("#OldPrice").val();
            let Pointno = $("#Pointno").val();
            let IsDiscountAllowed = ($('#IsDiscountAllowed').is(":checked") == true) ? true : false;
            let IsShippingFree = ($('#IsShippingFree').is(":checked") == true) ? true : false;
            let ShippingCharges = $("#ShippingCharges").val();
            let EstimatedShippingDays = $("#EstimatedShippingDays").val();
            let IsReturnAble = ($('#IsReturnAble').is(":checked") == true) ? true : false;
            let InventoryMethodID = $("#InventoryMethodID").val();
            let WarehouseId = $("#WarehouseId").val();
            let StockQuantity = $("#StockQuantity").val();
            let IsBoundToStockQuantity = ($('#IsBoundToStockQuantity').is(":checked") == true) ? true : false;
            let DisplayStockQuantity = ($('#DisplayStockQuantity').is(":checked") == true) ? true : false;
            let OrderMinimumQuantity = $("#OrderMinimumQuantity").val();
            let OrderMaximumQuantity = $("#OrderMaximumQuantity").val();
            let MetaTitle = $("#MetaTitle").val();
            let MetaKeywords = $("#MetaKeywords").val();
            let MetaDescription = $("#MetaDescription").val();
            let IsDigitalProduct = ($('#IsDigitalProduct').is(":checked") == true) ? true : false;

             var ProductImages = $("#ProductImages").get(0).files;
          var videofile = $("#videofile").get(0).files;

            // ✅ select2 javascript drodown values gathering
            let SelectedCategoryIdsArray = $("#SelectedCategoryIds").select2('data');
            let SelectedCategoriesJson = (SelectedCategoryIdsArray != null && SelectedCategoryIdsArray != undefined && SelectedCategoryIdsArray.length > 0) ? (SelectedCategoryIdsArray.map(function (a) { return a.id; })).toString() : "";
            let SelectedTagIdsArray = $("#SelectedTagIds").select2('data');
            let SelectedTagsJson = (SelectedTagIdsArray != null && SelectedTagIdsArray != undefined && SelectedTagIdsArray.length > 0) ? (SelectedTagIdsArray.map(function (a) { return a.id; })).toString() : "";
            let SelectedDiscountIdsArray = $("#SelectedDiscountIds").select2('data');
            let SelectedDiscountsJson = (SelectedDiscountIdsArray != null && SelectedDiscountIdsArray != undefined && SelectedDiscountIdsArray.length > 0) ? (SelectedDiscountIdsArray.map(function (a) { return a.id; })).toString() : "";

            let SelectedShippingMethodIdsArray = $("#SelectedShippingMethodIds").select2('data');
            let SelectedShippingMethodsJson = (SelectedShippingMethodIdsArray != null && SelectedShippingMethodIdsArray != undefined && SelectedShippingMethodIdsArray.length > 0) ? (SelectedShippingMethodIdsArray.map(function (a) { return a.id; })).toString() : "";


            if (!checkIfStringIsEmtpy(SelectedCategoriesJson)) {
                showSuccessErrorMsg("error", "Error", "Please select category");
                return false;
            }

            if (ProductImages == null || ProductImages == undefined || ProductImages.length == undefined || ProductImages.length < 1) {
                showSuccessErrorMsg("error", "Error", "Please select at least one product image!");
                return false;
            }

            //--check if no category selected
            if (!checkIfStringIsEmtpy(SelectedCategoriesJson)) {
                showSuccessErrorMsg("error", "Error", "Please select category");
                return false;
            }

            //-->> check if digital product validation starts here
            let DownloadUrlOption = $("#DownloadUrlOption").val();
            let DigitalProductExistingUrl = "";
            if (IsDigitalProduct != undefined && IsDigitalProduct == true) {
                if (DownloadUrlOption == 1) {//--Existing url
                    DigitalProductExistingUrl = $("#DigitalProductExistingUrl").val();
                    if (!checkIfStringIsEmtpy(DigitalProductExistingUrl)) {
                        showSuccessErrorMsg("error", "Error", "Please give an existing url becuase you selected digital product true!");
                        return false;
                    }
                } else if (DownloadUrlOption == 2) {//--New file
                    var DigitalProductNewFileUpload = $("#DigitalProductNewFileUpload").get(0).files;
                    if (DigitalProductNewFileUpload == null || DigitalProductNewFileUpload == undefined ||
                        DigitalProductNewFileUpload.length == undefined || DigitalProductNewFileUpload.length < 1) {
                        showSuccessErrorMsg("error", "Error", "Please upload a file for the digital product");
                        return false;
                    }
                } else {
                    showSuccessErrorMsg("error", "Error", "Please provide all information for the digital product");
                    return false;
                }
            }
            //--<< check if digital product validation ends here

            if (checkIfStringIsEmtpy(FullDescription)) {

                FullDescription = setProductDescriptionImagesUrl(FullDescription);
            }

            //-- #endRegion All fields values getting area ends here

            //--#Region product attributes get values
            var ProductAttributesTableRows = [];

            $('.product-attribute-row').each(function () {


                let data_ProductAttributeId = $(this).attr("data_ProductAttributeId");
                let data_AttributeValue = $(this).attr("data_AttributeValue");
                let data_PriceAdjustmentType = $(this).attr("data_PriceAdjustmentType");
                let data_PriceAdjustment = $(this).attr("data_PriceAdjustment");

                if (!checkIfStringIsEmtpy(data_ProductAttributeId) || !checkIfStringIsEmtpy(data_AttributeValue)) {
                    return false;
                }

                ProductAttributesTableRows.push({
                    ProductAttributeId: data_ProductAttributeId,
                    AttributeValue: data_AttributeValue,
                    PriceAdjustmentType: data_PriceAdjustmentType,
                    PriceAdjustment: data_PriceAdjustment

                });
            });

            var ProductAttributesJson = ProductAttributesTableRows.length == 0 ? "[]" : JSON.stringify(ProductAttributesTableRows);

            //--#endRegion product attributes


            //-- #region Form initialization area starts here
            var fileData = new FormData();

              for (var i = 0; i < ProductImages.length; i++) {
                fileData.append("ProductImages", ProductImages[i]);
            }
             for (var i = 0; i < videofile.length; i++) {
                fileData.append("videofile", videofile[i]);
            }
            fileData.append("ProductName", ProductName);
            fileData.append("ShortDescription", ShortDescription);
            fileData.append("FullDescription", FullDescription);
        
            fileData.append("producttypeId", producttypeId);
            fileData.append("ManufacturerId", ManufacturerId);
            fileData.append("VendorId", VendorId);
            fileData.append("IsActive", IsActive);
            fileData.append("ShowOnHomePage", ShowOnHomePage);
            fileData.append("MarkAsNew", MarkAsNew);
            fileData.append("AllowCustomerReviews", AllowCustomerReviews);
            fileData.append("SellStartDatetimeUtc", SellStartDatetimeUtc);
            fileData.append("SellEndDatetimeUtc", SellEndDatetimeUtc);
            fileData.append("Sku", Sku);
            fileData.append("Price", Price);
            fileData.append("OldPrice", OldPrice);
             fileData.append("Pointno", Pointno);
            fileData.append("IsDiscountAllowed", IsDiscountAllowed);
            fileData.append("IsShippingFree", IsShippingFree);
            fileData.append("ShippingCharges", ShippingCharges);
            fileData.append("EstimatedShippingDays", EstimatedShippingDays);
            fileData.append("IsReturnAble", IsReturnAble);
            fileData.append("InventoryMethodID", InventoryMethodID);
            fileData.append("WarehouseId", WarehouseId);
            fileData.append("StockQuantity", StockQuantity);
            fileData.append("IsBoundToStockQuantity", IsBoundToStockQuantity);
            fileData.append("DisplayStockQuantity", DisplayStockQuantity);
            fileData.append("OrderMinimumQuantity", OrderMinimumQuantity);
            fileData.append("OrderMaximumQuantity", OrderMaximumQuantity);
            fileData.append("MetaTitle", MetaTitle);
            fileData.append("MetaKeywords", MetaKeywords);
            fileData.append("MetaDescription", MetaDescription);

            //--digital product
            fileData.append("IsDigitalProduct", IsDigitalProduct);
            fileData.append("DownloadUrlOption", DownloadUrlOption);
            fileData.append("DigitalProductExistingUrl", DigitalProductExistingUrl);
            if (DigitalProductNewFileUpload != null && DigitalProductNewFileUpload != undefined && DigitalProductNewFileUpload.length > 0) {
                for (var i = 0; i < DigitalProductNewFileUpload.length; i++) {
                    fileData.append("DigitalProductNewFileUpload", DigitalProductNewFileUpload[i]);
                }
            }


            fileData.append("SelectedCategoriesJson", SelectedCategoriesJson);
            fileData.append("SelectedTagsJson", SelectedTagsJson);
            fileData.append("SelectedDiscountsJson", SelectedDiscountsJson);
            fileData.append("SelectedShippingMethodsJson", SelectedShippingMethodsJson);
            fileData.append("ProductAttributesJson", ProductAttributesJson);
            //-- #endRegion Form initialization area ends here


            // ✅ Show loader area starts here
            showHideSiteMainLoader(true);
            // ✅ Show loader area ends here



            let saveUrl = "@Url.Action("CreateNewProductPost", "ProductsCatalog" , new { langCode = Model?.PageBasicInfoObj?.langCode })";

            $.ajax({
                type: "POST",
                url: saveUrl,
                dataType: "json",
                contentType: false, // Not to set any content header
                processData: false, // Not to process data
                data: fileData,
                success: function (data) {

                    // ✅ Stop loader area starts here
                    let LoaderTimeDuration = '@ConstantsHelper.SiteMainLoaderDuration()';
                    setTimeout(function () {
                        showHideSiteMainLoader(false);
                    }, LoaderTimeDuration ?? 2000);
                    // ✅ Stop loader area ends here

                    if (data.success) {
                        showSuccessErrorMsg("success", "Success", data.message);

                        setTimeout(function () {
                            window.location.href = "@Url.Action("ProductsList","ProductsCatalog" , new { langCode = Model?.PageBasicInfoObj?.langCode })";
                        }, 1000);

                    }
                    else {

                        showSuccessErrorMsg("error", "Error", data.message ?? "An error occured. Please try again!");
                    }
                },
                error: function (xhr, status, error) {

                    showSuccessErrorMsg("error", "Error", "Something went wrong. Please try again");

                    // ✅ Stop loader area starts here
                    let LoaderTimeDuration = '@ConstantsHelper.SiteMainLoaderDuration()';
                    setTimeout(function () {
                        showHideSiteMainLoader(false);
                    }, LoaderTimeDuration ?? 2000);
                    // ✅ Stop loader area ends here
                }
            });

        }



        function EnableShippingChargesBox() {
            let IsShippingFree = ($('#IsShippingFree').is(":checked") == true) ? true : false;
            if (IsShippingFree) {
                $("#ShippingChargesDiv").css("display", "none");
                $("#ShippingCharges").val("");
            } else {
                $("#ShippingChargesDiv").css("display", "flex");
            }
        }
        function EnableDigitalProdFileUploadDiv() {
            let DownloadUrlOption = $('#DownloadUrlOption').val();

            if (DownloadUrlOption == 2) {
                $("#DigitalProductNewFileUploadDiv").css("display", "flex");
                $("#DigitalProductExistingUrlDiv").css("display", "none");

                $("#DigitalProductExistingUrl").val("");

            } else {
                $("#DigitalProductNewFileUploadDiv").css("display", "none");
                $("#DigitalProductExistingUrlDiv").css("display", "flex");
            }
        }

        function EnableDigitalProductBox() {
            let IsDigitalProduct = ($('#IsDigitalProduct').is(":checked") == true) ? true : false;
            if (IsDigitalProduct) {
                $(".DownloadUrlOptionDiv").css("display", "flex");

            } else {
                $(".DownloadUrlOptionDiv").css("display", "none");
                $("#DigitalProductExistingUrlDiv").css("display", "none");
                $("#DigitalProductNewFileUploadDiv").css("display", "none");

            }
        }

        function EnableDiscountsDropDown() {
            let IsDiscountAllowed = ($('#IsDiscountAllowed').is(":checked") == true) ? true : false;
            if (IsDiscountAllowed) {
                $("#SelectedDiscountIdsDiv").css("display", "flex");

            } else {
                $("#SelectedDiscountIdsDiv").css("display", "none");
                $("#SelectedDiscountIds").val("");
                $("#SelectedDiscountIds").val("").trigger("change");
            }
        }



        function GetProductAttributeValuesByAttributeID() {


            let ProductAttributeId = $('#ProductAttributeId').val();

            if (!checkIfStringIsEmtpy(ProductAttributeId)) {
                return false;
            }


            //--make form data
            var formDate = {
                ProductAttributeId: ProductAttributeId
            }

            let saveUrl = "@Url.Action("GetProductAttributeValuesByAttributeID", "ProductsCatalog" , new { langCode = Model?.PageBasicInfoObj?.langCode })";
            $.ajax({
                type: "GET",
                url: saveUrl,
                data: formDate,
                // datatype: "json",
                cache: false,
                async: false,

                success: function (data) {

                    if (data.success) {
                        let s = '<option value="">Select an Attribute value...</option>';
                        for (var i = 0; i < data.result.length; i++) {
                            s += '<option value="' + data.result[i].displayValue + '">' + data.result[i].displayText + '</option>';
                        } $("#AttributeValue").html(s);
                    } else {
                        showSuccessErrorMsg("error", "Error", data.message ?? "An error occured. Please try again!");
                    }

                },
                error: function (xhr, ajaxOptions, thrownError) {
                    showSuccessErrorMsg("error", "Error", "An error occured. Please try again!");
                }
            })

        }


        function AddProductAttributeRow() {
            event.preventDefault();


            //--get values of fields
            let ProductAttributeId = $("#ProductAttributeId").val();
            let PriceAdjustmentType = $("#PriceAdjustmentType").val();
            let PriceAdjustment = $("#PriceAdjustment").val();
            let AttributeValue = $("#AttributeValue").val();

            PriceAdjustment = PriceAdjustment == undefined || PriceAdjustment < 0 || PriceAdjustment == '' ? 0 : PriceAdjustment;

            //--get text of fields
            let ProductAttributeIdText = $("#ProductAttributeId option:selected").text();
            let AttributeValueText = $("#AttributeValue option:selected").text();
            let PriceAdjustmentTypeText = $("#PriceAdjustmentType option:selected").text();

            //--check for null or empty
            if (!checkIfStringIsEmtpy(ProductAttributeId)) {
                showSuccessErrorMsg("error", "Error", "Please select product attribute!");
                return false;
            }
            if (!checkIfStringIsEmtpy(PriceAdjustmentType)) {
                showSuccessErrorMsg("error", "Error", "Please select price adjustment type!");
                return false;
            }
            if (!checkIfStringIsEmtpy(AttributeValue)) {
                showSuccessErrorMsg("error", "Error", "Please select attribute value type!");
                return false;
            }


            //--check if row already exists in table
            let isRowExists = false;
            $('.product-attribute-row').each(function () {

                let data_ProductAttributeId = $(this).attr("data_ProductAttributeId");
                let data_AttributeValue = $(this).attr("data_AttributeValue");
                if (ProductAttributeId == data_ProductAttributeId && AttributeValue == data_AttributeValue) {
                    isRowExists = true;
                }

            });

            if (isRowExists == true) {
                event.preventDefault();
                showSuccessErrorMsg("error", "Error", "This attribute already exists!");
                return false;
            }

            //--check if no data row exists in table, then remove
            if ($("#product_attribute_no_data_row").length) {
                $("#product_attribute_no_data_row").remove();
            }

            //--Make table row
            $('#product_attributes_table tbody').append("<tr>"
                + "<td> "
                + "<input type='hidden' data_ProductAttributeMappingID='-1' class='product-attribute-row' data_ProductAttributeId='" + ProductAttributeId + "' data_AttributeValue='" + AttributeValue + "'  data_PriceAdjustmentType='" + PriceAdjustmentType + "' data_PriceAdjustment='" + PriceAdjustment + "'/>"
                + ProductAttributeIdText
                + "</td>"
                + "<td> " + AttributeValueText + "</td>"
                + "<td> " + PriceAdjustmentTypeText + "</td>"
                + "<td> " + PriceAdjustment + "</td>"
                + "<td>"
                + "<div class=''>"

                + "<a href='#!' class='dropdown-item text-pink-600' onclick='DeleteProductAttributeRow( this,-1);'>"
                + "<i class='icon-trash'></i> Delete"
                + "</a>"
                + "</div> "
                + "</td>"
                + "</tr>");


        }


        function DeleteProductAttributeRow(thisParam, ProductAttributeMappingID) {
            event.preventDefault();

            let confirmClick = confirm('Do you really want to remove this record?');
            if (confirmClick) {
                debugger
                if (ProductAttributeMappingID == -1) {//-- delete only temperory row that is not saved on data base
                    $(thisParam).closest('tr').remove();
                    return true;
                } else {//--delete permanently

                    var formData = {
                        EntityId: '@(Model?.PageBasicInfoObj?.EntityId ?? 0)',
                        primarykeyValue: ProductAttributeMappingID,
                        primaryKeyColumn: "ProductAttributeMappingID",
                        tableName: "Product_ProductAttribute_Mapping",
                        SqlDeleteType: "@((short)SqlDeleteTypes.PlainTableDelete)"

                    }

                    $.ajax({
                        type: "POST",
                        url: '@Url.Action("DeleteAnyRecord", "Dynamic")',
                        data: formData,
                        success: function (data) {

                            if (data.success) {
                                showSuccessErrorMsg("success", "Success", data.message);
                                $(thisParam).closest('tr').remove();
                            }
                            else {

                                showSuccessErrorMsg("error", "Error", "Something went wrong during saving of record");
                            }


                        },
                        error: function (xhr, ajaxOptions, thrownError) {

                            showSuccessErrorMsg("error", "Error", "An error occured. Please try again.");
                        }
                    });
                }

            }

        }

    </script>

}