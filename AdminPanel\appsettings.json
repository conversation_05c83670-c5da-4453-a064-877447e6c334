{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },


  "ConnectionStrings": {
    "DBConnection": "Data Source=mssql.codemedicalapps.com;Initial Catalog=codemedi_shop;User ID=codemedi_shop; Password=*********;"
    // "DBConnection": "Data Source=plesk7100.is.cc;Initial Catalog=perfectj_codemedical;User ID=perfectj_codemedical; Password=*********;"

    //"DBConnection": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=ECommerceShopDB;Integrated Security=True;"
  },

  "AppSetting": {
     "WebsiteBaseURL": "http://localhost:3000",
   // "WebsiteBaseURL": "https://codemedicalapps.com",
    //"ApiValidationIssuer": "https://localhost:7149/",
     "ApiValidationIssuer": "http://admin.codemedicalapps.com/",
    "ApiAuthorizationEnabledGlobally": "false",
    "WebsiteTitle": "Code Medical",
   // "AdminPanelBaseUrl": "https://localhost:7149",
   "AdminPanelBaseUrl": "http://admin.codemedicalapps.com/",
    "ListingItemsPerPage": "10",
    "CookiesExpiryDurationInDays": "2",
    "ProductsImagesDirectory": "/content/commonImages/productImages",
    "DigitalProductsFilesDirectory": "/content/commonImages/digitalProductFiles",
    "OtherImagesDirectory": "/content/commonImages/otherImages",
    "RoleRightsEnables": "1",
    "StripeSecretKey": "sk_test_7H0xAULZgPj1VU2hmdaXkyV6",
    "StripePublishableKey": "pk_test_Iobqeuqx5q774gLZMhh18zQn",
    "DefaultCurrencyCode": "USD",
    "DefaultCurrencySymbol": "$"

  },

  "EmailConfiguration": {
    "From": "<EMAIL>",
    "SmtpServer": "mail.codemedicalapps.com",
    "Port": 465,
    "Username": "<EMAIL>",
    "Password": "5?jc02mY9",
    "EmailSetupHelpLink": "https://code-maze.com/aspnetcore-send-email/"
  },


  "AllowedHosts": "*"
}
