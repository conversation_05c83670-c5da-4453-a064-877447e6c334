'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { motion } from 'framer-motion';
import { Mail, Lock, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { cn } from '@/lib/utils';
import { PasswordInput } from '@/components/ui/password-input';

export default function Login() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [loginDetails, setLoginDetails] = useState({
    email: '',
    password: ''
  });
  const { t } = useSettings();
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: ''
  });

  const validateForm = () => {
    const errors = {
      email: '',
      password: ''
    };
    let isValid = true;

    if (!loginDetails.email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginDetails.email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    if (!loginDetails.password.trim()) {
      errors.password = 'Password is required';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isValid = validateForm();
    if (!isValid) {
      return;
    }
    setLoading(true);
    setError('');

    try {
      // TODO: Implement login logic here
      console.log('Login details:', loginDetails);
    } catch (err: any) {
      setError(err.message || 'Failed to login');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold">
            Welcome Back
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            Sign in to your account
          </p>
        </div>

        <Card className="mt-8 p-8 shadow-xl bg-card">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <form onSubmit={handleLoginSubmit} className="space-y-6">
              <div>
                <Label className="block text-sm font-medium mb-2">Email</Label>
                <div className="relative">
                  <Input
                    type="email"
                    value={loginDetails.email}
                    onChange={(e) => {
                      setLoginDetails({...loginDetails, email: e.target.value});
                      if (formErrors.email) validateForm();
                    }}
                    className={cn("pl-10", formErrors.email && "border-red-500")}
                    required
                    disabled={loading}
                  />
                  <Mail className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                </div>
                {formErrors.email && (
                  <p className="mt-1 text-sm text-destructive">{formErrors.email}</p>
                )}
              </div>
              <div>
                <Label className="block text-sm font-medium mb-2">Password</Label>
                <div className="relative">
                  <PasswordInput
                    value={loginDetails.password}
                    onChange={(e) => {
                      setLoginDetails({...loginDetails, password: e.target.value});
                      if (formErrors.password) validateForm();
                    }}
                    className={cn("pl-10", formErrors.password && "border-red-500")}
                    required
                    disabled={loading}
                  />
                  <Lock className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                </div>
                {formErrors.password && (
                  <p className="mt-1 text-sm text-destructive">{formErrors.password}</p>
                )}
              </div>

              {error && (
                <div className="text-red-500 text-sm text-center">{error}</div>
              )}

              <Button
                type="submit"
                className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                disabled={loading}
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  'Sign In'
                )}
              </Button>

              <div className="text-center text-sm">
                <span className="text-muted-foreground">Don't have an account? </span>
                <Link href="/signup" className="text-primary hover:text-primary/80 hover:underline transition-colors">
                  Sign up
                </Link>
              </div>
            </form>
          </motion.div>
        </Card>
      </div>
    </div>
  );
}