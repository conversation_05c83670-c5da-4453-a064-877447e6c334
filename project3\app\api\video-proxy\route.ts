import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get('url');
    
    if (!url) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }

    const videoResponse = await fetch(url, {
      headers: {
        'Range': request.headers.get('range') || 'bytes=0-',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!videoResponse.ok) {
      return new NextResponse('Failed to fetch video', { status: videoResponse.status });
    }

    const headers = new Headers();
    
    // Copy content type and content length
    headers.set('Content-Type', videoResponse.headers.get('Content-Type') || 'video/mp4');
    headers.set('Content-Length', videoResponse.headers.get('Content-Length') || '');
    headers.set('Accept-Ranges', 'bytes');
    
    // Copy range headers for streaming
    if (videoResponse.headers.get('Content-Range')) {
      headers.set('Content-Range', videoResponse.headers.get('Content-Range') || '');
    }
    
    // Set CORS headers
    headers.set('Access-Control-Allow-Origin', '*');
    headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Range');

    return new NextResponse(videoResponse.body, {
      status: videoResponse.status,
      headers
    });
  } catch (error) {
    console.error('Video proxy error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function OPTIONS() {
  const headers = new Headers();
  headers.set('Access-Control-Allow-Origin', '*');
  headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Range');
  
  return new NextResponse(null, {
    status: 204,
    headers
  });
}