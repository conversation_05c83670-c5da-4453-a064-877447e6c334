"use client"

import { useEffect, useState, useRef } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button" // Fixed import path for Button

interface Category {
  id: number
  title: string
  image: string
  parentId?: number
}

export default function PopularCategories() {
  // Replace the settings context with a simple translation function
  const t = (key: string) => {
    const translations: Record<string, string> = {
      popularCategories: "Popular Categories",
      // Add more translations as needed
    }
    return translations[key] || key
  }

  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(0)
  const carouselRef = useRef<HTMLDivElement>(null)

  const [autoplay, setAutoplay] = useState(true)
  const [autoplaySpeed] = useState(5000) // 5 seconds per slide
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Number of items to show per page based on screen size
  const itemsPerPage = {
    sm: 6, // Small screens - Show 6 items (3 per row)
    md: 6, // Medium screens - Show 6 items (2 per row)
    lg: 8, // Large screens - Show 8 items (4 per row)
    xl: 10, // Extra large screens - Show 10 items (5 per row)
  }

  // Calculate total pages based on number of categories and items per page
  // Use a responsive approach to determine items per page
  const getItemsPerPage = () => {
    // This is a client-side calculation, so we need to check if window is defined
    if (typeof window !== "undefined") {
      const width = window.innerWidth
      if (width >= 1280) return itemsPerPage.xl // xl screens
      if (width >= 1024) return itemsPerPage.lg // lg screens
      if (width >= 768) return itemsPerPage.md // md screens
      return itemsPerPage.sm // sm screens
    }
    return itemsPerPage.lg // Default for SSR
  }

  const currentItemsPerPage = getItemsPerPage()
  const totalPages = Math.ceil(categories.length / currentItemsPerPage)

  // Navigation functions for the carousel
  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1)
    } else {
      // Loop back to the first page
      setCurrentPage(0)
    }
  }

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    } else {
      // Loop to the last page
      setCurrentPage(totalPages - 1)
    }
  }

  const startAutoplay = () => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current)
    }

    autoplayTimerRef.current = setInterval(() => {
      nextPage()
    }, autoplaySpeed)
  }

  const stopAutoplay = () => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current)
      autoplayTimerRef.current = null
    }
  }

  const handleInteraction = () => {
    // Stop autoplay on user interaction
    stopAutoplay()

    // Resume autoplay after 10 seconds of inactivity
    setTimeout(() => {
      if (autoplay) {
        startAutoplay()
      }
    }, 10000)
  }

  const constructImageUrl = (attachmentUrl: string | null) => {
    if (!attachmentUrl) return "https://admin.codemedicalapps.com/images/no-image.jpg"

    // Check if the URL already has the full domain
    if (attachmentUrl.startsWith("http")) {
      return attachmentUrl
    }

    // Base URL
    const baseUrl = "https://admin.codemedicalapps.com"

    // Make sure attachmentUrl starts with a slash
    const normalizedAttachmentUrl = attachmentUrl.startsWith("/") ? attachmentUrl : `/${attachmentUrl}`

    // Construct the full URL
    return `${baseUrl}${normalizedAttachmentUrl}`
  }

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true)

        // Direct axios implementation instead of using MakeApiCallAsync
        const axios = (await import("axios")).default

        const param = {
          requestParameters: {
            recordValueJson: "[]",
          },
        }

        const config = {
          method: "post",
          url: "https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-popular-categories",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          data: param,
        }

        // Make the API call
        const response = await axios(config)

        // Process the response
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data)

            if (Array.isArray(parsedData)) {
              // Map the data to our category format
              const popularCategories = parsedData.map((item) => ({
                id: item.CategoryID,
                title: item.Name,
                image: constructImageUrl(item.AttachmentURL),
                parentId: undefined,
              }))

              setCategories(popularCategories)
            } else {
              console.error("Categories data is not an array:", parsedData)
              setCategories([])
            }
          } catch (parseError) {
            console.error("Error parsing data:", parseError)
            setCategories([])
          }
        } else {
          console.error("No data returned from API")
          setCategories([])
        }
      } catch (error) {
        console.error("Error fetching categories:", error)
        setCategories([])
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  // Add window resize listener to update carousel when screen size changes
  useEffect(() => {
    const handleResize = () => {
      // Recalculate items per page and total pages
      const newItemsPerPage = getItemsPerPage()
      const newTotalPages = Math.ceil(categories.length / newItemsPerPage)

      // Adjust current page if needed
      if (currentPage >= newTotalPages) {
        setCurrentPage(Math.max(0, newTotalPages - 1))
      }
    }

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [categories.length, currentPage])

  // Set up autoplay
  useEffect(() => {
    if (autoplay && categories.length > 0) {
      startAutoplay()
    } else {
      stopAutoplay()
    }

    // Clean up on unmount
    return () => {
      stopAutoplay()
    }
  }, [autoplay, categories.length, autoplaySpeed])

  if (loading) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="animate-pulse text-lg">Loading popular categories...</div>
          </div>
        </div>
      </section>
    )
  }

  if (!categories.length) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="text-lg text-gray-500">No categories available at the moment. Please check back later.</div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">{t("popularCategories")}</h2>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                prevPage()
                handleInteraction()
              }}
              className="rounded-full"
              aria-label="Previous page"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                nextPage()
                handleInteraction()
              }}
              className="rounded-full"
              aria-label="Next page"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div
          className="w-full relative overflow-hidden"
          onMouseEnter={() => stopAutoplay()}
          onMouseLeave={() => autoplay && startAutoplay()}
        >
          <div
            ref={carouselRef}
            className="w-full transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${currentPage * 100}%)`,
            }}
          >
            <div className="flex flex-nowrap" style={{ width: `${totalPages * 100}%` }}>
              {/* Show all categories in groups */}
              {Array.from({ length: totalPages }).map((_, pageIndex) => (
                <div
                  key={`page-${pageIndex}`}
                  className="w-full flex-shrink-0 grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8"
                  style={{ width: `${100 / totalPages}%` }}
                >
                  {categories
                    .slice(pageIndex * currentItemsPerPage, (pageIndex + 1) * currentItemsPerPage)
                    .map((category) => (
                      <div key={category.id} className="flex flex-col items-center px-2">
                        <a
                          href={`/products?category=${category.id}`}
                          className="group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105"
                        >
                          <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300">
                              <div className="w-full h-full rounded-full overflow-hidden border-2 border-white bg-white">
                                <img
                                  src={category.image || "/placeholder.svg?height=150&width=150"}
                                  alt={category.title}
                                  width={144}
                                  height={144}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    console.error("Image load error for:", category.image)

                                    if (
                                      category.image !== "https://admin.codemedicalapps.com/images/no-image.jpg" &&
                                      !category.image.includes("no-image")
                                    ) {
                                      const baseUrl = "https://admin.codemedicalapps.com"
                                      const directUrl = `${baseUrl}/images/no-image.jpg`
                                      console.log("Trying fallback URL:", directUrl)
                                      target.src = directUrl

                                      target.onerror = () => {
                                        console.error("Fallback URL also failed, using simple placeholder")
                                        target.src = "/placeholder.svg?height=150&width=150"
                                        target.onerror = null
                                      }
                                    } else {
                                      target.src = "/placeholder.svg?height=150&width=150"
                                    }
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <h3 className="text-sm sm:text-base font-medium text-gray-800 text-center group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                            {category.title}
                          </h3>
                        </a>
                      </div>
                    ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pagination indicators */}
        <div className="flex justify-center mt-4 space-x-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={`indicator-${index}`}
              className={`h-2 rounded-full transition-all ${
                currentPage === index ? "w-6 bg-primary" : "w-2 bg-gray-300"
              }`}
              onClick={() => {
                setCurrentPage(index)
                handleInteraction()
              }}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  )
}